require("dotenv").config()

const fs = require("fs")
const { readStreamFromS3 } = require("./AwsManager")

const downloadImage = async () => {
    const originalKey = "hawak-dev/product/mobile/1131/686e52fa5907c74a61c5a84b_P3.jpeg"
    const localPath = "./downloaded-image.jpeg"
    
    console.log("📥 Downloading image from S3...")
    console.log("Key:", originalKey)
    console.log("Local path:", localPath)
    
    try {
        const readStream = readStreamFromS3(originalKey)
        const writeStream = fs.createWriteStream(localPath)
        
        readStream.on('error', (error) => {
            console.error("❌ S3 Read Error:", error)
        })
        
        writeStream.on('error', (error) => {
            console.error("❌ Write Error:", error)
        })
        
        readStream.pipe(writeStream)
        
        await new Promise((resolve, reject) => {
            writeStream.on('finish', () => {
                console.log("✅ Image downloaded successfully!")
                
                const stats = fs.statSync(localPath)
                console.log("File size:", stats.size, "bytes")
                
                resolve()
            })
            
            writeStream.on('error', reject)
            readStream.on('error', reject)
        })
        
    } catch (error) {
        console.error("💥 Download failed:", error)
    }
}

downloadImage().catch(console.error)
