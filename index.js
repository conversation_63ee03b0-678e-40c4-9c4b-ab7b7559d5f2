require("dotenv").config()

const path = require("path")

const {
    IMAGE_TYPE,
    IMAGE_PATH,
    RESIZE_OPTIONS,
} = require("./constants")

const {
    readStreamFromS3,
    writeStreamToS3,
} = require("./AwsManager")

const {
    streamToSharp,
    logTimeMessage,
} = require("./Utils")

const compressAndUpload = async (originalKey, imageType, Key) => {
    const cuStartTime = performance.now()
    // create the read and write streams from and to S3 and the Sharp stream
    const readStream = readStreamFromS3(originalKey)

    const ext = path.extname(Key)
    const options = RESIZE_OPTIONS[imageType]

    // trigger the sharp stream
    const spStartTime = performance.now()
    const sharpStream = streamToSharp(ext, options)
    const spEndTime = performance.now()
    logTimeMessage(`Sharp Stream (${imageType})`, spStartTime, spEndTime)

    const { writeStream, uploadFinished } = writeStreamToS3(Key, ext)

    // trigger the stream
    const tsStartTime = performance.now()

    readStream
        .pipe(sharpStream)
        .pipe(writeStream)

    const tsEndTime = performance.now()
    logTimeMessage(`Trigger Stream (${imageType})`, tsStartTime, tsEndTime)

    // wait for the stream to finish
    const ufStartTime = performance.now()
    const uploadedData = await uploadFinished
    const ufEndTime = performance.now()
    logTimeMessage(`Writing Stream (${imageType})`, ufStartTime, ufEndTime)

    console.log(`Uploaded file (${imageType}) to:`, uploadedData.Location)
    const cuEndTime = performance.now()
    logTimeMessage(`compressAndUpload (${imageType})`, cuStartTime, cuEndTime)
}

exports.handler = async function (event) {
    try {
        const lambdaStartTime = performance.now()
        console.log("Event", JSON.stringify(event))

        const processingKeys = []

        for (let index = 0; index < event["Records"].length; index++) {
            const record = event["Records"][index]
            const s3Object = record["s3"]["object"]
            const originalKey = s3Object["key"]

            let logoKey,
                thumbnailKey,
                miniThumbnailKey,
                userThumbKey,
                sysUserThumbKey

            const isMobileCompression = originalKey.includes(IMAGE_PATH.MOBILE)
            const isUserCompression = originalKey.includes(IMAGE_PATH.USER_PROFILE)
            const isSysUserCompression = originalKey.includes(IMAGE_PATH.SYSTEM_USER_PROFILE)

            if (isMobileCompression) {
                logoKey = originalKey.replace(
                    IMAGE_PATH.MOBILE,
                    IMAGE_PATH.LOGO,
                )

                thumbnailKey = originalKey.replace(
                    IMAGE_PATH.MOBILE,
                    IMAGE_PATH.THUMBNAIL,
                )

                miniThumbnailKey = originalKey.replace(
                    IMAGE_PATH.MOBILE,
                    IMAGE_PATH.MINI_THUMBNAIL,
                )
            }

            if (isUserCompression) {
                userThumbKey = originalKey.replace(
                    IMAGE_PATH.USER_PROFILE,
                    IMAGE_PATH.COMPRESSED.USER_PROFILE_THUMBNAIL,
                )
            }

            if (isSysUserCompression) {
                sysUserThumbKey = originalKey.replace(
                    IMAGE_PATH.SYSTEM_USER_PROFILE,
                    IMAGE_PATH.COMPRESSED.SYSTEM_USER_PROFILE_THUMBNAIL,
                )
            }

            processingKeys.push({
                originalKey,
                logoKey,
                thumbnailKey,
                miniThumbnailKey,
                userThumbKey,
                sysUserThumbKey,
            })
        }

        await Promise.allSettled(
            processingKeys.flatMap(item => {
                const {
                    originalKey,
                    logoKey,
                    thumbnailKey,
                    miniThumbnailKey,
                    userThumbKey,
                    sysUserThumbKey,
                } = item

                const promises = []

                if (logoKey) {
                    promises.push(
                        compressAndUpload(
                            originalKey,
                            IMAGE_TYPE.LOGO,
                            logoKey,
                        ),
                        compressAndUpload(
                            originalKey,
                            IMAGE_TYPE.THUMBNAIL,
                            thumbnailKey,
                        ),
                        compressAndUpload(
                            originalKey,
                            IMAGE_TYPE.MINI_THUMBNAIL,
                            miniThumbnailKey,
                        ),
                    )
                }

                if (userThumbKey) {
                    promises.push(
                        compressAndUpload(
                            originalKey,
                            IMAGE_TYPE.USER_PROFILE_THUMBNAIL,
                            userThumbKey,
                        )
                    )
                }

                if (sysUserThumbKey) {
                    promises.push(
                        compressAndUpload(
                            originalKey,
                            IMAGE_TYPE.SYSTEM_USER_PROFILE_THUMBNAIL,
                            sysUserThumbKey,
                        )
                    )
                }
                return promises
            })
        )

        const lambdaEndTime = performance.now()
        logTimeMessage("Total lambda function", lambdaStartTime, lambdaEndTime)
    }
    catch (error) {
        console.log(error)
    }
}
