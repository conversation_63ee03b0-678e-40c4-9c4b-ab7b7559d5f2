require("dotenv").config()

const path = require("path")

const {
    IMAGE_TYPE,
    IMAGE_PATH,
    RESIZE_OPTIONS,
} = require("./constants")

const {
    readStreamFromS3,
    writeStreamToS3,
} = require("./AwsManager")

const {
    streamToSharp,
    logTimeMessage,
} = require("./Utils")

const compressAndUpload = async (originalKey, imageType, Key) => {
    try {
        console.log(`Starting compression for ${imageType}:`, { originalKey, Key })
        const cuStartTime = performance.now()

        // create the read and write streams from and to S3 and the Sharp stream
        const readStream = readStreamFromS3(originalKey)

        const ext = path.extname(Key)
        const options = RESIZE_OPTIONS[imageType]

        console.log(`Processing ${imageType} with options:`, { ext, options })

        // trigger the sharp stream
        const spStartTime = performance.now()
        const sharpStream = streamToSharp(ext, options)
        const spEndTime = performance.now()
        logTimeMessage(`Sharp Stream (${imageType})`, spStartTime, spEndTime)

        const { writeStream, uploadFinished } = writeStreamToS3(Key, ext)

        // Add error handling for streams
        readStream.on('error', (err) => {
            console.error(`Read stream error for ${imageType}:`, err)
        })

        sharpStream.on('error', (err) => {
            console.error(`Sharp stream error for ${imageType}:`, err)
        })

        writeStream.on('error', (err) => {
            console.error(`Write stream error for ${imageType}:`, err)
        })

        // trigger the stream
        const tsStartTime = performance.now()

        readStream
            .pipe(sharpStream)
            .pipe(writeStream)

        const tsEndTime = performance.now()
        logTimeMessage(`Trigger Stream (${imageType})`, tsStartTime, tsEndTime)

        // wait for the stream to finish
        const ufStartTime = performance.now()
        const uploadedData = await uploadFinished
        const ufEndTime = performance.now()
        logTimeMessage(`Writing Stream (${imageType})`, ufStartTime, ufEndTime)

        console.log(`Uploaded file (${imageType}) to:`, uploadedData.Location)
        const cuEndTime = performance.now()
        logTimeMessage(`compressAndUpload (${imageType})`, cuStartTime, cuEndTime)

        return uploadedData
    } catch (error) {
        console.error(`Error in compressAndUpload for ${imageType}:`, error)
        throw error
    }
}

exports.handler = async function (event) {
    try {
        const lambdaStartTime = performance.now()
        console.log("Event", JSON.stringify(event))

        const processingKeys = []

        for (let index = 0; index < event["Records"].length; index++) {
            const record = event["Records"][index]
            const s3Object = record["s3"]["object"]
            const originalKey = s3Object["key"]

            console.log(`Processing record ${index + 1}/${event["Records"].length}:`, { originalKey })

            let logoKey,
                thumbnailKey,
                miniThumbnailKey,
                userThumbKey,
                sysUserThumbKey

            const isMobileCompression = originalKey.includes(IMAGE_PATH.MOBILE)
            const isUserCompression = originalKey.includes(IMAGE_PATH.USER_PROFILE)
            const isSysUserCompression = originalKey.includes(IMAGE_PATH.SYSTEM_USER_PROFILE)

            console.log(`Path matching results:`, {
                isMobileCompression,
                isUserCompression,
                isSysUserCompression,
                mobilePath: IMAGE_PATH.MOBILE,
                userPath: IMAGE_PATH.USER_PROFILE,
                sysUserPath: IMAGE_PATH.SYSTEM_USER_PROFILE
            })

            if (isMobileCompression) {
                logoKey = originalKey.replace(
                    IMAGE_PATH.MOBILE,
                    IMAGE_PATH.LOGO,
                )

                thumbnailKey = originalKey.replace(
                    IMAGE_PATH.MOBILE,
                    IMAGE_PATH.THUMBNAIL,
                )

                miniThumbnailKey = originalKey.replace(
                    IMAGE_PATH.MOBILE,
                    IMAGE_PATH.MINI_THUMBNAIL,
                )

                console.log(`Generated mobile compression keys:`, {
                    logoKey,
                    thumbnailKey,
                    miniThumbnailKey
                })
            }

            if (isUserCompression) {
                userThumbKey = originalKey.replace(
                    IMAGE_PATH.USER_PROFILE,
                    IMAGE_PATH.COMPRESSED.USER_PROFILE_THUMBNAIL,
                )
                console.log(`Generated user compression key:`, { userThumbKey })
            }

            if (isSysUserCompression) {
                sysUserThumbKey = originalKey.replace(
                    IMAGE_PATH.SYSTEM_USER_PROFILE,
                    IMAGE_PATH.COMPRESSED.SYSTEM_USER_PROFILE_THUMBNAIL,
                )
                console.log(`Generated system user compression key:`, { sysUserThumbKey })
            }

            processingKeys.push({
                originalKey,
                logoKey,
                thumbnailKey,
                miniThumbnailKey,
                userThumbKey,
                sysUserThumbKey,
            })
        }

        console.log(`Starting processing for ${processingKeys.length} items`)

        const results = await Promise.allSettled(
            processingKeys.flatMap(item => {
                const {
                    originalKey,
                    logoKey,
                    thumbnailKey,
                    miniThumbnailKey,
                    userThumbKey,
                    sysUserThumbKey,
                } = item

                const promises = []

                if (logoKey) {
                    console.log(`Adding mobile compression tasks for: ${originalKey}`)
                    promises.push(
                        compressAndUpload(
                            originalKey,
                            IMAGE_TYPE.LOGO,
                            logoKey,
                        ),
                        compressAndUpload(
                            originalKey,
                            IMAGE_TYPE.THUMBNAIL,
                            thumbnailKey,
                        ),
                        compressAndUpload(
                            originalKey,
                            IMAGE_TYPE.MINI_THUMBNAIL,
                            miniThumbnailKey,
                        ),
                    )
                }

                if (userThumbKey) {
                    console.log(`Adding user profile compression task for: ${originalKey}`)
                    promises.push(
                        compressAndUpload(
                            originalKey,
                            IMAGE_TYPE.USER_PROFILE_THUMBNAIL,
                            userThumbKey,
                        )
                    )
                }

                if (sysUserThumbKey) {
                    console.log(`Adding system user profile compression task for: ${originalKey}`)
                    promises.push(
                        compressAndUpload(
                            originalKey,
                            IMAGE_TYPE.SYSTEM_USER_PROFILE_THUMBNAIL,
                            sysUserThumbKey,
                        )
                    )
                }

                if (promises.length === 0) {
                    console.log(`No compression tasks generated for: ${originalKey}`)
                }

                return promises
            })
        )

        // Log results
        console.log(`Processing completed. Results summary:`)
        results.forEach((result, index) => {
            if (result.status === 'fulfilled') {
                console.log(`Task ${index + 1}: SUCCESS`)
            } else {
                console.error(`Task ${index + 1}: FAILED -`, result.reason)
            }
        })

        const lambdaEndTime = performance.now()
        logTimeMessage("Total lambda function", lambdaStartTime, lambdaEndTime)

        console.log("Lambda execution completed successfully")
    }
    catch (error) {
        console.error("Lambda execution failed:", error)
        console.error("Error stack:", error.stack)
        throw error // Re-throw to ensure Lambda marks this as failed
    }
}
