require("dotenv").config()

const path = require("path")

const {
    IMAGE_TYPE,
    IMAGE_PATH,
    RESIZE_OPTIONS,
} = require("./constants")

const {
    readStreamFromS3,
    writeStreamToS3,
} = require("./AwsManager")

const {
    streamToSharp,
    logTimeMessage,
} = require("./Utils")

const compressAndUpload = async (originalKey, imageType, Key) => {
    try {
        console.log(`🔄 Starting ${imageType} processing:`, { originalKey, Key })
        const cuStartTime = performance.now()

        // Log image details
        const ext = path.extname(Key)
        const options = RESIZE_OPTIONS[imageType]
        console.log(`📋 Processing details:`, {
            imageType,
            ext,
            options,
            originalKey,
            targetKey: Key
        })

        // create the read and write streams from and to S3 and the Sharp stream
        console.log(`📥 Creating read stream from S3...`)
        const readStream = readStreamFromS3(originalKey)

        // Add error handling for read stream
        readStream.on('error', (error) => {
            console.error(`❌ S3 Read Stream Error for ${imageType}:`, {
                originalKey,
                error: error.message,
                code: error.code,
                statusCode: error.statusCode
            })
        })

        // trigger the sharp stream
        console.log(`🖼️ Creating Sharp stream...`)
        const spStartTime = performance.now()
        const sharpStream = streamToSharp(ext, options)
        const spEndTime = performance.now()
        logTimeMessage(`Sharp Stream (${imageType})`, spStartTime, spEndTime)

        // Add error handling for sharp stream
        sharpStream.on('error', (error) => {
            console.error(`❌ Sharp Processing Error for ${imageType}:`, {
                originalKey,
                Key,
                error: error.message,
                stack: error.stack
            })
        })

        console.log(`📤 Creating write stream to S3...`)
        const { writeStream, uploadFinished } = writeStreamToS3(Key, ext)

        // Add error handling for write stream
        writeStream.on('error', (error) => {
            console.error(`❌ S3 Write Stream Error for ${imageType}:`, {
                Key,
                error: error.message,
                code: error.code,
                statusCode: error.statusCode
            })
        })

        // trigger the stream
        console.log(`🔗 Connecting streams...`)
        const tsStartTime = performance.now()

        readStream
            .pipe(sharpStream)
            .pipe(writeStream)

        const tsEndTime = performance.now()
        logTimeMessage(`Trigger Stream (${imageType})`, tsStartTime, tsEndTime)

        // wait for the stream to finish
        console.log(`⏳ Waiting for upload to complete...`)
        const ufStartTime = performance.now()
        const uploadedData = await uploadFinished
        const ufEndTime = performance.now()
        logTimeMessage(`Writing Stream (${imageType})`, ufStartTime, ufEndTime)

        console.log(`✅ Successfully uploaded ${imageType}:`, uploadedData.Location)
        const cuEndTime = performance.now()
        logTimeMessage(`compressAndUpload (${imageType})`, cuStartTime, cuEndTime)

        return uploadedData
    } catch (error) {
        console.error(`💥 Fatal error in compressAndUpload for ${imageType}:`, {
            originalKey,
            Key,
            imageType,
            error: error.message,
            stack: error.stack,
            code: error.code
        })
        throw error
    }
}

exports.handler = async function (event) {
    try {
        const lambdaStartTime = performance.now()
        console.log("Event", JSON.stringify(event))

        const processingKeys = []

        for (let index = 0; index < event["Records"].length; index++) {
            const record = event["Records"][index]
            const s3Object = record["s3"]["object"]
            const originalKey = s3Object["key"]

            console.log(`🔍 Processing S3 event ${index + 1}/${event["Records"].length}:`, {
                originalKey,
                bucket: record.s3.bucket.name,
                size: s3Object.size,
                eventName: record.eventName,
                eventTime: record.eventTime
            })

            // Special logging for the problematic image
            if (originalKey.includes("686e52fa5907c74a61c5a84b_P3.jpeg")) {
                console.log(`🎯 FOUND PROBLEMATIC IMAGE: ${originalKey}`)
                console.log(`📊 Image details:`, {
                    size: s3Object.size,
                    etag: s3Object.eTag,
                    bucket: record.s3.bucket.name,
                    region: record.awsRegion
                })
            }

            let logoKey,
                thumbnailKey,
                miniThumbnailKey,
                userThumbKey,
                sysUserThumbKey

            const isMobileCompression = originalKey.includes(IMAGE_PATH.MOBILE)
            const isUserCompression = originalKey.includes(IMAGE_PATH.USER_PROFILE)
            const isSysUserCompression = originalKey.includes(IMAGE_PATH.SYSTEM_USER_PROFILE)

            console.log(`🔍 Path analysis for: ${originalKey}`, {
                isMobileCompression,
                isUserCompression,
                isSysUserCompression,
                mobilePath: IMAGE_PATH.MOBILE,
                userPath: IMAGE_PATH.USER_PROFILE,
                sysUserPath: IMAGE_PATH.SYSTEM_USER_PROFILE
            })

            if (isMobileCompression) {
                logoKey = originalKey.replace(
                    IMAGE_PATH.MOBILE,
                    IMAGE_PATH.LOGO,
                )

                thumbnailKey = originalKey.replace(
                    IMAGE_PATH.MOBILE,
                    IMAGE_PATH.THUMBNAIL,
                )

                miniThumbnailKey = originalKey.replace(
                    IMAGE_PATH.MOBILE,
                    IMAGE_PATH.MINI_THUMBNAIL,
                )

                console.log(`📝 Generated mobile keys:`, {
                    originalKey,
                    logoKey,
                    thumbnailKey,
                    miniThumbnailKey
                })
            }

            if (isUserCompression) {
                userThumbKey = originalKey.replace(
                    IMAGE_PATH.USER_PROFILE,
                    IMAGE_PATH.COMPRESSED.USER_PROFILE_THUMBNAIL,
                )
            }

            if (isSysUserCompression) {
                sysUserThumbKey = originalKey.replace(
                    IMAGE_PATH.SYSTEM_USER_PROFILE,
                    IMAGE_PATH.COMPRESSED.SYSTEM_USER_PROFILE_THUMBNAIL,
                )
            }

            processingKeys.push({
                originalKey,
                logoKey,
                thumbnailKey,
                miniThumbnailKey,
                userThumbKey,
                sysUserThumbKey,
            })
        }

        console.log(`🚀 Starting processing for ${processingKeys.length} items`)

        const results = await Promise.allSettled(
            processingKeys.flatMap(item => {
                const {
                    originalKey,
                    logoKey,
                    thumbnailKey,
                    miniThumbnailKey,
                    userThumbKey,
                    sysUserThumbKey,
                } = item

                const promises = []

                if (logoKey) {
                    console.log(`📱 Adding mobile compression tasks for: ${originalKey}`)
                    promises.push(
                        compressAndUpload(
                            originalKey,
                            IMAGE_TYPE.LOGO,
                            logoKey,
                        ),
                        compressAndUpload(
                            originalKey,
                            IMAGE_TYPE.THUMBNAIL,
                            thumbnailKey,
                        ),
                        compressAndUpload(
                            originalKey,
                            IMAGE_TYPE.MINI_THUMBNAIL,
                            miniThumbnailKey,
                        ),
                    )
                } else {
                    console.log(`⚠️ No logoKey generated for: ${originalKey}`)
                }

                if (userThumbKey) {
                    console.log(`👤 Adding user profile compression task for: ${originalKey}`)
                    promises.push(
                        compressAndUpload(
                            originalKey,
                            IMAGE_TYPE.USER_PROFILE_THUMBNAIL,
                            userThumbKey,
                        )
                    )
                }

                if (sysUserThumbKey) {
                    console.log(`🔧 Adding system user profile compression task for: ${originalKey}`)
                    promises.push(
                        compressAndUpload(
                            originalKey,
                            IMAGE_TYPE.SYSTEM_USER_PROFILE_THUMBNAIL,
                            sysUserThumbKey,
                        )
                    )
                }

                if (promises.length === 0) {
                    console.log(`❌ No compression tasks generated for: ${originalKey}`)
                }

                console.log(`📊 Generated ${promises.length} tasks for: ${originalKey}`)
                return promises
            })
        )

        // Detailed results logging
        console.log(`📈 Processing completed. Analyzing ${results.length} results:`)
        let successCount = 0
        let failureCount = 0

        results.forEach((result, index) => {
            if (result.status === 'fulfilled') {
                successCount++
                console.log(`✅ Task ${index + 1}: SUCCESS`)
            } else {
                failureCount++
                console.error(`❌ Task ${index + 1}: FAILED`, {
                    reason: result.reason?.message || result.reason,
                    stack: result.reason?.stack,
                    code: result.reason?.code
                })
            }
        })

        console.log(`📊 Final Summary: ${successCount} successful, ${failureCount} failed out of ${results.length} total tasks`)

        const lambdaEndTime = performance.now()
        logTimeMessage("Total lambda function", lambdaStartTime, lambdaEndTime)
    }
    catch (error) {
        console.log(error)
    }
}
