const sharp = require("sharp")

const {
    EXTENSIONS,
} = require("./constants")

const logTimeMessage = (message, startTime, endTime) => {
    console.log(`${message} Time(seconds):`, ((endTime - startTime) / 1000))
}

// sharp stream
const streamToSharp = (ext, options) => {
    const force =
        ext === EXTENSIONS.JPEG
            ? true // Default value. Force JPEG output
            : false // Doesn't force JPEG output, use input format

    return sharp()
        .keepExif()
        .resize(options)
        .jpeg({
            force,
        })
}

module.exports = {
    logTimeMessage,
    streamToSharp,
}
