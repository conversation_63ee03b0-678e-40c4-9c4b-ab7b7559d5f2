{"version": "2.0", "metadata": {"apiVersion": "2018-05-10", "endpointPrefix": "datazone", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "Amazon DataZone", "serviceId": "DataZone", "signatureVersion": "v4", "signingName": "datazone", "uid": "datazone-2018-05-10"}, "operations": {"AcceptPredictions": {"http": {"method": "PUT", "requestUri": "/v2/domains/{domainIdentifier}/assets/{identifier}/accept-predictions", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"acceptChoices": {"type": "list", "member": {"type": "structure", "required": ["predictionTarget"], "members": {"editedValue": {"type": "string", "sensitive": true}, "predictionChoice": {"type": "integer"}, "predictionTarget": {}}}}, "acceptRule": {"type": "structure", "members": {"rule": {}, "threshold": {"type": "float"}}}, "clientToken": {"idempotencyToken": true}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "revision": {"location": "querystring", "locationName": "revision"}}}, "output": {"type": "structure", "required": ["assetId", "domainId", "revision"], "members": {"assetId": {}, "domainId": {}, "revision": {}}}, "idempotent": true}, "AcceptSubscriptionRequest": {"http": {"method": "PUT", "requestUri": "/v2/domains/{domainIdentifier}/subscription-requests/{identifier}/accept", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"decisionComment": {"shape": "Sh"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "id", "requestReason", "status", "subscribedListings", "subscribedPrincipals", "updatedAt"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "decisionComment": {"shape": "Sh"}, "domainId": {}, "id": {}, "requestReason": {"shape": "Sm"}, "reviewerId": {}, "status": {}, "subscribedListings": {"type": "list", "member": {"shape": "Sp"}}, "subscribedPrincipals": {"type": "list", "member": {"shape": "S16"}}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}, "idempotent": true}, "AssociateEnvironmentRole": {"http": {"method": "PUT", "requestUri": "/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/roles/{environmentRoleArn}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentIdentifier", "environmentRoleArn"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {"location": "uri", "locationName": "environmentIdentifier"}, "environmentRoleArn": {"location": "uri", "locationName": "environmentRoleArn"}}}, "output": {"type": "structure", "members": {}}}, "CancelMetadataGenerationRun": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/metadata-generation-runs/{identifier}/cancel", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "members": {}}}, "CancelSubscription": {"http": {"method": "PUT", "requestUri": "/v2/domains/{domainIdentifier}/subscriptions/{identifier}/cancel", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "id", "status", "subscribedListing", "subscribedPrincipal", "updatedAt"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "id": {}, "retainPermissions": {"type": "boolean"}, "status": {}, "subscribedListing": {"shape": "Sp"}, "subscribedPrincipal": {"shape": "S16"}, "subscriptionRequestId": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}, "idempotent": true}, "CreateAsset": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/assets", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "name", "owningProjectIdentifier", "typeIdentifier"], "members": {"clientToken": {"idempotencyToken": true}, "description": {"shape": "Sq"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "externalIdentifier": {"shape": "S1n"}, "formsInput": {"shape": "S1o"}, "glossaryTerms": {"shape": "S1t"}, "name": {"shape": "S1v"}, "owningProjectIdentifier": {}, "predictionConfiguration": {"shape": "S1w"}, "typeIdentifier": {}, "typeRevision": {}}}, "output": {"type": "structure", "required": ["domainId", "formsOutput", "id", "name", "owningProjectId", "revision", "typeIdentifier", "typeRevision"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "Sq"}, "domainId": {}, "externalIdentifier": {"shape": "S1n"}, "firstRevisionCreatedAt": {"type": "timestamp"}, "firstRevisionCreatedBy": {}, "formsOutput": {"shape": "S20"}, "glossaryTerms": {"shape": "S1t"}, "id": {}, "latestTimeSeriesDataPointFormsOutput": {"shape": "S23"}, "listing": {"shape": "S29"}, "name": {"shape": "S1v"}, "owningProjectId": {}, "predictionConfiguration": {"shape": "S1w"}, "readOnlyFormsOutput": {"shape": "S20"}, "revision": {}, "typeIdentifier": {}, "typeRevision": {}}}, "idempotent": true}, "CreateAssetFilter": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/assets/{assetIdentifier}/filters", "responseCode": 201}, "input": {"type": "structure", "required": ["assetIdentifier", "configuration", "domainIdentifier", "name"], "members": {"assetIdentifier": {"location": "uri", "locationName": "assetIdentifier"}, "clientToken": {"idempotencyToken": true}, "configuration": {"shape": "S2c"}, "description": {"shape": "Sq"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "name": {"shape": "S2w"}}}, "output": {"type": "structure", "required": ["assetId", "configuration", "domainId", "id", "name"], "members": {"assetId": {}, "configuration": {"shape": "S2c"}, "createdAt": {"type": "timestamp"}, "description": {"shape": "Sq"}, "domainId": {}, "effectiveColumnNames": {"shape": "S2e"}, "effectiveRowFilter": {}, "errorMessage": {}, "id": {}, "name": {"shape": "S2w"}, "status": {}}}, "idempotent": true}, "CreateAssetRevision": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/assets/{identifier}/revisions", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier", "name"], "members": {"clientToken": {"idempotencyToken": true}, "description": {"shape": "Sq"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "formsInput": {"shape": "S1o"}, "glossaryTerms": {"shape": "S1t"}, "identifier": {"location": "uri", "locationName": "identifier"}, "name": {"shape": "S1v"}, "predictionConfiguration": {"shape": "S1w"}, "typeRevision": {}}}, "output": {"type": "structure", "required": ["domainId", "formsOutput", "id", "name", "owningProjectId", "revision", "typeIdentifier", "typeRevision"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "Sq"}, "domainId": {}, "externalIdentifier": {"shape": "S1n"}, "firstRevisionCreatedAt": {"type": "timestamp"}, "firstRevisionCreatedBy": {}, "formsOutput": {"shape": "S20"}, "glossaryTerms": {"shape": "S1t"}, "id": {}, "latestTimeSeriesDataPointFormsOutput": {"shape": "S23"}, "listing": {"shape": "S29"}, "name": {"shape": "S1v"}, "owningProjectId": {}, "predictionConfiguration": {"shape": "S1w"}, "readOnlyFormsOutput": {"shape": "S20"}, "revision": {}, "typeIdentifier": {}, "typeRevision": {}}}, "idempotent": true}, "CreateAssetType": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/asset-types", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "formsInput", "name", "owningProjectIdentifier"], "members": {"description": {"shape": "Sq"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "formsInput": {"type": "map", "key": {}, "value": {"type": "structure", "required": ["typeIdentifier", "typeRevision"], "members": {"required": {"type": "boolean"}, "typeIdentifier": {}, "typeRevision": {}}}}, "name": {}, "owningProjectIdentifier": {}}}, "output": {"type": "structure", "required": ["domainId", "formsOutput", "name", "revision"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "Sq"}, "domainId": {}, "formsOutput": {"shape": "S36"}, "name": {}, "originDomainId": {}, "originProjectId": {}, "owningProjectId": {}, "revision": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "CreateDataProduct": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/data-products", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "name", "owningProjectIdentifier"], "members": {"clientToken": {"idempotencyToken": true}, "description": {"shape": "S39"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "formsInput": {"shape": "S1o"}, "glossaryTerms": {"shape": "S1t"}, "items": {"shape": "S3a"}, "name": {"shape": "S3f"}, "owningProjectIdentifier": {}}}, "output": {"type": "structure", "required": ["domainId", "id", "name", "owningProjectId", "revision", "status"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "S39"}, "domainId": {}, "firstRevisionCreatedAt": {"type": "timestamp"}, "firstRevisionCreatedBy": {}, "formsOutput": {"shape": "S20"}, "glossaryTerms": {"shape": "S1t"}, "id": {}, "items": {"shape": "S3a"}, "name": {"shape": "S3f"}, "owningProjectId": {}, "revision": {}, "status": {}}}, "idempotent": true}, "CreateDataProductRevision": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/data-products/{identifier}/revisions", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier", "name"], "members": {"clientToken": {"idempotencyToken": true}, "description": {"shape": "S39"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "formsInput": {"shape": "S1o"}, "glossaryTerms": {"shape": "S1t"}, "identifier": {"location": "uri", "locationName": "identifier"}, "items": {"shape": "S3a"}, "name": {"shape": "S3f"}}}, "output": {"type": "structure", "required": ["domainId", "id", "name", "owningProjectId", "revision", "status"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "S39"}, "domainId": {}, "firstRevisionCreatedAt": {"type": "timestamp"}, "firstRevisionCreatedBy": {}, "formsOutput": {"shape": "S20"}, "glossaryTerms": {"shape": "S1t"}, "id": {}, "items": {"shape": "S3a"}, "name": {"shape": "S3f"}, "owningProjectId": {}, "revision": {}, "status": {}}}, "idempotent": true}, "CreateDataSource": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/data-sources", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentIdentifier", "name", "projectIdentifier", "type"], "members": {"assetFormsInput": {"shape": "S1o"}, "clientToken": {"idempotencyToken": true}, "configuration": {"shape": "S3m"}, "description": {"shape": "Sq"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "enableSetting": {}, "environmentIdentifier": {}, "name": {"shape": "S47"}, "projectIdentifier": {}, "publishOnImport": {"type": "boolean"}, "recommendation": {"shape": "S48"}, "schedule": {"shape": "S49"}, "type": {}}}, "output": {"type": "structure", "required": ["domainId", "environmentId", "id", "name", "projectId"], "members": {"assetFormsOutput": {"shape": "S20"}, "configuration": {"shape": "S4e"}, "createdAt": {"shape": "S4n"}, "description": {"shape": "Sq"}, "domainId": {}, "enableSetting": {}, "environmentId": {}, "errorMessage": {"shape": "S4o"}, "id": {}, "lastRunAt": {"shape": "S4n"}, "lastRunErrorMessage": {"shape": "S4o"}, "lastRunStatus": {}, "name": {"shape": "S47"}, "projectId": {}, "publishOnImport": {"type": "boolean"}, "recommendation": {"shape": "S48"}, "schedule": {"shape": "S49"}, "status": {}, "type": {}, "updatedAt": {"shape": "S4n"}}}, "idempotent": true}, "CreateDomain": {"http": {"requestUri": "/v2/domains", "responseCode": 201}, "input": {"type": "structure", "required": ["domainExecutionRole", "name"], "members": {"clientToken": {"idempotencyToken": true}, "description": {}, "domainExecutionRole": {}, "kmsKeyIdentifier": {}, "name": {}, "singleSignOn": {"shape": "S4w"}, "tags": {"shape": "S4z"}}}, "output": {"type": "structure", "required": ["id"], "members": {"arn": {}, "description": {}, "domainExecutionRole": {}, "id": {}, "kmsKeyIdentifier": {}, "name": {}, "portalUrl": {}, "singleSignOn": {"shape": "S4w"}, "status": {}, "tags": {"shape": "S4z"}}}, "idempotent": true}, "CreateEnvironment": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/environments", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentProfileIdentifier", "name", "projectIdentifier"], "members": {"description": {}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentAccountIdentifier": {}, "environmentAccountRegion": {}, "environmentBlueprintIdentifier": {}, "environmentProfileIdentifier": {}, "glossaryTerms": {"shape": "S1t"}, "name": {}, "projectIdentifier": {}, "userParameters": {"shape": "S56"}}}, "output": {"type": "structure", "required": ["created<PERSON>y", "domainId", "name", "projectId", "provider"], "members": {"awsAccountId": {}, "awsAccountRegion": {}, "createdAt": {"shape": "S5b"}, "createdBy": {}, "deploymentProperties": {"shape": "S5c"}, "description": {"shape": "Sq"}, "domainId": {}, "environmentActions": {"shape": "S5f"}, "environmentBlueprintId": {}, "environmentProfileId": {}, "glossaryTerms": {"shape": "S1t"}, "id": {}, "lastDeployment": {"shape": "S5l"}, "name": {"shape": "S5r"}, "projectId": {}, "provider": {}, "provisionedResources": {"shape": "S5s"}, "provisioningProperties": {"shape": "S5u"}, "status": {}, "updatedAt": {"shape": "S5b"}, "userParameters": {"shape": "S5x"}}}}, "CreateEnvironmentAction": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/actions", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentIdentifier", "name", "parameters"], "members": {"description": {}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {"location": "uri", "locationName": "environmentIdentifier"}, "name": {}, "parameters": {"shape": "S61"}}}, "output": {"type": "structure", "required": ["domainId", "environmentId", "id", "name", "parameters"], "members": {"description": {}, "domainId": {}, "environmentId": {}, "id": {}, "name": {}, "parameters": {"shape": "S61"}}}}, "CreateEnvironmentProfile": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/environment-profiles", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentBlueprintIdentifier", "name", "projectIdentifier"], "members": {"awsAccountId": {}, "awsAccountRegion": {}, "description": {"shape": "Sq"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentBlueprintIdentifier": {}, "name": {"shape": "S66"}, "projectIdentifier": {}, "userParameters": {"shape": "S56"}}}, "output": {"type": "structure", "required": ["created<PERSON>y", "domainId", "environmentBlueprintId", "id", "name"], "members": {"awsAccountId": {}, "awsAccountRegion": {}, "createdAt": {"shape": "S5b"}, "createdBy": {}, "description": {"shape": "Sq"}, "domainId": {}, "environmentBlueprintId": {}, "id": {}, "name": {"shape": "S66"}, "projectId": {}, "updatedAt": {"shape": "S5b"}, "userParameters": {"shape": "S5x"}}}}, "CreateFormType": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/form-types", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "model", "name", "owningProjectIdentifier"], "members": {"description": {"shape": "Sq"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "model": {"shape": "S69"}, "name": {"shape": "S22"}, "owningProjectIdentifier": {}, "status": {}}}, "output": {"type": "structure", "required": ["domainId", "name", "revision"], "members": {"description": {"shape": "Sq"}, "domainId": {}, "name": {"shape": "S22"}, "originDomainId": {}, "originProjectId": {}, "owningProjectId": {}, "revision": {}}}}, "CreateGlossary": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/glossaries", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "name", "owningProjectIdentifier"], "members": {"clientToken": {"idempotencyToken": true}, "description": {"shape": "S6e"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "name": {"shape": "S6f"}, "owningProjectIdentifier": {}, "status": {}}}, "output": {"type": "structure", "required": ["domainId", "id", "name", "owningProjectId"], "members": {"description": {"shape": "S6e"}, "domainId": {}, "id": {}, "name": {"shape": "S6f"}, "owningProjectId": {}, "status": {}}}, "idempotent": true}, "CreateGlossaryTerm": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/glossary-terms", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "glossaryIdentifier", "name"], "members": {"clientToken": {"idempotencyToken": true}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "glossaryIdentifier": {}, "longDescription": {"shape": "S6k"}, "name": {"shape": "Sy"}, "shortDescription": {"shape": "Sz"}, "status": {}, "termRelations": {"shape": "S6m"}}}, "output": {"type": "structure", "required": ["domainId", "glossaryId", "id", "name", "status"], "members": {"domainId": {}, "glossaryId": {}, "id": {}, "longDescription": {"shape": "S6k"}, "name": {"shape": "Sy"}, "shortDescription": {"shape": "Sz"}, "status": {}, "termRelations": {"shape": "S6m"}}}, "idempotent": true}, "CreateGroupProfile": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/group-profiles", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "groupIdentifier"], "members": {"clientToken": {"idempotencyToken": true}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "groupIdentifier": {}}}, "output": {"type": "structure", "members": {"domainId": {}, "groupName": {"shape": "S6t"}, "id": {}, "status": {}}}, "idempotent": true}, "CreateListingChangeSet": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/listings/change-set", "responseCode": 200}, "input": {"type": "structure", "required": ["action", "domainIdentifier", "entityIdentifier", "entityType"], "members": {"action": {}, "clientToken": {"idempotencyToken": true}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "entityIdentifier": {}, "entityRevision": {}, "entityType": {}}}, "output": {"type": "structure", "required": ["listingId", "listingRevision", "status"], "members": {"listingId": {}, "listingRevision": {}, "status": {}}}}, "CreateProject": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/projects", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "name"], "members": {"description": {"shape": "Sq"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "glossaryTerms": {"shape": "S1t"}, "name": {"shape": "S18"}}}, "output": {"type": "structure", "required": ["created<PERSON>y", "domainId", "id", "name"], "members": {"createdAt": {"shape": "S5b"}, "createdBy": {}, "description": {"shape": "Sq"}, "domainId": {}, "failureReasons": {"shape": "S72"}, "glossaryTerms": {"shape": "S1t"}, "id": {}, "lastUpdatedAt": {"shape": "S5b"}, "name": {"shape": "S18"}, "projectStatus": {}}}}, "CreateProjectMembership": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/projects/{projectIdentifier}/createMembership", "responseCode": 201}, "input": {"type": "structure", "required": ["designation", "domainIdentifier", "member", "projectIdentifier"], "members": {"designation": {}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "member": {"shape": "S77"}, "projectIdentifier": {"location": "uri", "locationName": "projectIdentifier"}}}, "output": {"type": "structure", "members": {}}}, "CreateSubscriptionGrant": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/subscription-grants", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentIdentifier", "grantedEntity", "subscriptionTargetIdentifier"], "members": {"assetTargetNames": {"type": "list", "member": {"type": "structure", "required": ["assetId", "targetName"], "members": {"assetId": {}, "targetName": {}}}}, "clientToken": {"idempotencyToken": true}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {}, "grantedEntity": {"type": "structure", "members": {"listing": {"type": "structure", "required": ["identifier", "revision"], "members": {"identifier": {}, "revision": {}}}}, "union": true}, "subscriptionTargetIdentifier": {}}}, "output": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "grantedEntity", "id", "status", "subscriptionTargetId", "updatedAt"], "members": {"assets": {"shape": "S7g"}, "createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "grantedEntity": {"shape": "S7k"}, "id": {}, "status": {}, "subscriptionId": {"deprecated": true, "deprecatedMessage": "Multiple subscriptions can exist for a single grant"}, "subscriptionTargetId": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "CreateSubscriptionRequest": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/subscription-requests", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "requestReason", "subscribedListings", "subscribedPrincipals"], "members": {"clientToken": {"idempotencyToken": true}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "requestReason": {"shape": "Sm"}, "subscribedListings": {"type": "list", "member": {"type": "structure", "required": ["identifier"], "members": {"identifier": {}}}}, "subscribedPrincipals": {"type": "list", "member": {"type": "structure", "members": {"project": {"type": "structure", "members": {"identifier": {}}}}, "union": true}}}}, "output": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "id", "requestReason", "status", "subscribedListings", "subscribedPrincipals", "updatedAt"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "decisionComment": {"shape": "Sh"}, "domainId": {}, "id": {}, "requestReason": {"shape": "Sm"}, "reviewerId": {}, "status": {}, "subscribedListings": {"type": "list", "member": {"shape": "Sp"}}, "subscribedPrincipals": {"type": "list", "member": {"shape": "S16"}}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "CreateSubscriptionTarget": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/subscription-targets", "responseCode": 200}, "input": {"type": "structure", "required": ["applicableAssetTypes", "authorizedPrincipals", "domainIdentifier", "environmentIdentifier", "manageAccessRole", "name", "subscriptionTargetConfig", "type"], "members": {"applicableAssetTypes": {"shape": "S7y"}, "authorizedPrincipals": {"shape": "S7z"}, "clientToken": {"idempotencyToken": true}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {"location": "uri", "locationName": "environmentIdentifier"}, "manageAccessRole": {}, "name": {"shape": "S81"}, "provider": {}, "subscriptionTargetConfig": {"shape": "S82"}, "type": {}}}, "output": {"type": "structure", "required": ["applicableAssetTypes", "authorizedPrincipals", "createdAt", "created<PERSON>y", "domainId", "environmentId", "id", "manageAccessRole", "name", "projectId", "provider", "subscriptionTargetConfig", "type"], "members": {"applicableAssetTypes": {"shape": "S7y"}, "authorizedPrincipals": {"shape": "S7z"}, "createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "environmentId": {}, "id": {}, "manageAccessRole": {}, "name": {"shape": "S81"}, "projectId": {}, "provider": {}, "subscriptionTargetConfig": {"shape": "S82"}, "type": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "CreateUserProfile": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/user-profiles", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "userIdentifier"], "members": {"clientToken": {"idempotencyToken": true}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "userIdentifier": {}, "userType": {}}}, "output": {"type": "structure", "members": {"details": {"shape": "S89"}, "domainId": {}, "id": {}, "status": {}, "type": {}}}, "idempotent": true}, "DeleteAsset": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/assets/{identifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeleteAssetFilter": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/assets/{assetIdentifier}/filters/{identifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["assetIdentifier", "domainIdentifier", "identifier"], "members": {"assetIdentifier": {"location": "uri", "locationName": "assetIdentifier"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "idempotent": true}, "DeleteAssetType": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/asset-types/{identifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "members": {}}}, "DeleteDataProduct": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/data-products/{identifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeleteDataSource": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/data-sources/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"clientToken": {"idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "retainPermissionsOnRevokeFailure": {"location": "querystring", "locationName": "retainPermissionsOnRevokeFailure", "type": "boolean"}}}, "output": {"type": "structure", "required": ["domainId", "environmentId", "id", "name", "projectId"], "members": {"assetFormsOutput": {"shape": "S20"}, "configuration": {"shape": "S4e"}, "createdAt": {"shape": "S4n"}, "description": {"shape": "Sq"}, "domainId": {}, "enableSetting": {}, "environmentId": {}, "errorMessage": {"shape": "S4o"}, "id": {}, "lastRunAt": {"shape": "S4n"}, "lastRunErrorMessage": {"shape": "S4o"}, "lastRunStatus": {}, "name": {"shape": "S47"}, "projectId": {}, "publishOnImport": {"type": "boolean"}, "retainPermissionsOnRevokeFailure": {"type": "boolean"}, "schedule": {"shape": "S49"}, "selfGrantStatus": {"shape": "S8r"}, "status": {}, "type": {}, "updatedAt": {"shape": "S4n"}}}, "idempotent": true}, "DeleteDomain": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{identifier}", "responseCode": 202}, "input": {"type": "structure", "required": ["identifier"], "members": {"clientToken": {"idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}, "identifier": {"location": "uri", "locationName": "identifier"}, "skipDeletionCheck": {"location": "querystring", "locationName": "skipDeletionCheck", "type": "boolean"}}}, "output": {"type": "structure", "required": ["status"], "members": {"status": {}}}, "idempotent": true}, "DeleteEnvironment": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/environments/{identifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "idempotent": true}, "DeleteEnvironmentAction": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/actions/{identifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {"location": "uri", "locationName": "environmentIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "idempotent": true}, "DeleteEnvironmentBlueprintConfiguration": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/environment-blueprint-configurations/{environmentBlueprintIdentifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentBlueprintIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentBlueprintIdentifier": {"location": "uri", "locationName": "environmentBlueprintIdentifier"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeleteEnvironmentProfile": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/environment-profiles/{identifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "idempotent": true}, "DeleteFormType": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/form-types/{formTypeIdentifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "formTypeIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "formTypeIdentifier": {"location": "uri", "locationName": "formTypeIdentifier"}}}, "output": {"type": "structure", "members": {}}}, "DeleteGlossary": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/glossaries/{identifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeleteGlossaryTerm": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/glossary-terms/{identifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeleteListing": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/listings/{identifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeleteProject": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/projects/{identifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "skipDeletionCheck": {"location": "querystring", "locationName": "skipDeletionCheck", "type": "boolean"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeleteProjectMembership": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/projects/{projectIdentifier}/deleteMembership", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "member", "projectIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "member": {"shape": "S77"}, "projectIdentifier": {"location": "uri", "locationName": "projectIdentifier"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeleteSubscriptionGrant": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/subscription-grants/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "grantedEntity", "id", "status", "subscriptionTargetId", "updatedAt"], "members": {"assets": {"shape": "S7g"}, "createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "grantedEntity": {"shape": "S7k"}, "id": {}, "status": {}, "subscriptionId": {"deprecated": true, "deprecatedMessage": "Multiple subscriptions can exist for a single grant"}, "subscriptionTargetId": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "DeleteSubscriptionRequest": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/subscription-requests/{identifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}}, "DeleteSubscriptionTarget": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/subscription-targets/{identifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {"location": "uri", "locationName": "environmentIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}}, "DeleteTimeSeriesDataPoints": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/entities/{entityType}/{entityIdentifier}/time-series-data-points", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "entityIdentifier", "entityType", "formName"], "members": {"clientToken": {"idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "entityIdentifier": {"location": "uri", "locationName": "entityIdentifier"}, "entityType": {"location": "uri", "locationName": "entityType"}, "formName": {"location": "querystring", "locationName": "formName"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DisassociateEnvironmentRole": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/roles/{environmentRoleArn}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentIdentifier", "environmentRoleArn"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {"location": "uri", "locationName": "environmentIdentifier"}, "environmentRoleArn": {"location": "uri", "locationName": "environmentRoleArn"}}}, "output": {"type": "structure", "members": {}}}, "GetAsset": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/assets/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "revision": {"location": "querystring", "locationName": "revision"}}}, "output": {"type": "structure", "required": ["domainId", "formsOutput", "id", "name", "owningProjectId", "revision", "typeIdentifier", "typeRevision"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "Sq"}, "domainId": {}, "externalIdentifier": {"shape": "S1n"}, "firstRevisionCreatedAt": {"type": "timestamp"}, "firstRevisionCreatedBy": {}, "formsOutput": {"shape": "S20"}, "glossaryTerms": {"shape": "S1t"}, "id": {}, "latestTimeSeriesDataPointFormsOutput": {"shape": "S23"}, "listing": {"shape": "S29"}, "name": {"shape": "S1v"}, "owningProjectId": {}, "readOnlyFormsOutput": {"shape": "S20"}, "revision": {}, "typeIdentifier": {}, "typeRevision": {}}}}, "GetAssetFilter": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/assets/{assetIdentifier}/filters/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["assetIdentifier", "domainIdentifier", "identifier"], "members": {"assetIdentifier": {"location": "uri", "locationName": "assetIdentifier"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["assetId", "configuration", "domainId", "id", "name"], "members": {"assetId": {}, "configuration": {"shape": "S2c"}, "createdAt": {"type": "timestamp"}, "description": {"shape": "Sq"}, "domainId": {}, "effectiveColumnNames": {"shape": "S2e"}, "effectiveRowFilter": {}, "errorMessage": {}, "id": {}, "name": {"shape": "S2w"}, "status": {}}}}, "GetAssetType": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/asset-types/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "revision": {"location": "querystring", "locationName": "revision"}}}, "output": {"type": "structure", "required": ["domainId", "formsOutput", "name", "owningProjectId", "revision"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "Sq"}, "domainId": {}, "formsOutput": {"shape": "S36"}, "name": {}, "originDomainId": {}, "originProjectId": {}, "owningProjectId": {}, "revision": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "GetDataProduct": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/data-products/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "revision": {"location": "querystring", "locationName": "revision"}}}, "output": {"type": "structure", "required": ["domainId", "id", "name", "owningProjectId", "revision", "status"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "S39"}, "domainId": {}, "firstRevisionCreatedAt": {"type": "timestamp"}, "firstRevisionCreatedBy": {}, "formsOutput": {"shape": "S20"}, "glossaryTerms": {"shape": "S1t"}, "id": {}, "items": {"shape": "S3a"}, "name": {"shape": "S3f"}, "owningProjectId": {}, "revision": {}, "status": {}}}}, "GetDataSource": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/data-sources/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["domainId", "environmentId", "id", "name", "projectId"], "members": {"assetFormsOutput": {"shape": "S20"}, "configuration": {"shape": "S4e"}, "createdAt": {"shape": "S4n"}, "description": {"shape": "Sq"}, "domainId": {}, "enableSetting": {}, "environmentId": {}, "errorMessage": {"shape": "S4o"}, "id": {}, "lastRunAssetCount": {"type": "integer"}, "lastRunAt": {"shape": "S4n"}, "lastRunErrorMessage": {"shape": "S4o"}, "lastRunStatus": {}, "name": {"shape": "S47"}, "projectId": {}, "publishOnImport": {"type": "boolean"}, "recommendation": {"shape": "S48"}, "schedule": {"shape": "S49"}, "selfGrantStatus": {"shape": "S8r"}, "status": {}, "type": {}, "updatedAt": {"shape": "S4n"}}}}, "GetDataSourceRun": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/data-source-runs/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["createdAt", "dataSourceId", "domainId", "id", "projectId", "status", "type", "updatedAt"], "members": {"createdAt": {"shape": "S4n"}, "dataSourceConfigurationSnapshot": {}, "dataSourceId": {}, "domainId": {}, "errorMessage": {"shape": "S4o"}, "id": {}, "projectId": {}, "runStatisticsForAssets": {"shape": "Sa4"}, "startedAt": {"shape": "S4n"}, "status": {}, "stoppedAt": {"shape": "S4n"}, "type": {}, "updatedAt": {"shape": "S4n"}}}}, "GetDomain": {"http": {"method": "GET", "requestUri": "/v2/domains/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["identifier"], "members": {"identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["domainExecutionRole", "id", "status"], "members": {"arn": {}, "createdAt": {"type": "timestamp"}, "description": {}, "domainExecutionRole": {}, "id": {}, "kmsKeyIdentifier": {}, "lastUpdatedAt": {"type": "timestamp"}, "name": {}, "portalUrl": {}, "singleSignOn": {"shape": "S4w"}, "status": {}, "tags": {"shape": "S4z"}}}}, "GetEnvironment": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/environments/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["created<PERSON>y", "domainId", "name", "projectId", "provider"], "members": {"awsAccountId": {}, "awsAccountRegion": {}, "createdAt": {"shape": "S5b"}, "createdBy": {}, "deploymentProperties": {"shape": "S5c"}, "description": {"shape": "Sq"}, "domainId": {}, "environmentActions": {"shape": "S5f"}, "environmentBlueprintId": {}, "environmentProfileId": {}, "glossaryTerms": {"shape": "S1t"}, "id": {}, "lastDeployment": {"shape": "S5l"}, "name": {"shape": "S5r"}, "projectId": {}, "provider": {}, "provisionedResources": {"shape": "S5s"}, "provisioningProperties": {"shape": "S5u"}, "status": {}, "updatedAt": {"shape": "S5b"}, "userParameters": {"shape": "S5x"}}}}, "GetEnvironmentAction": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/actions/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {"location": "uri", "locationName": "environmentIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["domainId", "environmentId", "id", "name", "parameters"], "members": {"description": {}, "domainId": {}, "environmentId": {}, "id": {}, "name": {}, "parameters": {"shape": "S61"}}}}, "GetEnvironmentBlueprint": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/environment-blueprints/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["id", "name", "provider", "provisioningProperties"], "members": {"createdAt": {"shape": "S5b"}, "deploymentProperties": {"shape": "S5c"}, "description": {"shape": "Sq"}, "glossaryTerms": {"shape": "S1t"}, "id": {}, "name": {}, "provider": {}, "provisioningProperties": {"shape": "S5u"}, "updatedAt": {"shape": "S5b"}, "userParameters": {"shape": "S5x"}}}}, "GetEnvironmentBlueprintConfiguration": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/environment-blueprint-configurations/{environmentBlueprintIdentifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentBlueprintIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentBlueprintIdentifier": {"location": "uri", "locationName": "environmentBlueprintIdentifier"}}}, "output": {"type": "structure", "required": ["domainId", "environmentBlueprintId"], "members": {"createdAt": {"shape": "S5b"}, "domainId": {}, "enabledRegions": {"shape": "Sah"}, "environmentBlueprintId": {}, "manageAccessRoleArn": {}, "provisioningConfigurations": {"shape": "<PERSON><PERSON>"}, "provisioningRoleArn": {}, "regionalParameters": {"shape": "Sao"}, "updatedAt": {"shape": "S5b"}}}}, "GetEnvironmentCredentials": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/credentials", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {"location": "uri", "locationName": "environmentIdentifier"}}}, "output": {"type": "structure", "members": {"accessKeyId": {}, "expiration": {"shape": "S5b"}, "secretAccessKey": {}, "sessionToken": {}}, "sensitive": true}}, "GetEnvironmentProfile": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/environment-profiles/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["created<PERSON>y", "domainId", "environmentBlueprintId", "id", "name"], "members": {"awsAccountId": {}, "awsAccountRegion": {}, "createdAt": {"shape": "S5b"}, "createdBy": {}, "description": {"shape": "Sq"}, "domainId": {}, "environmentBlueprintId": {}, "id": {}, "name": {"shape": "S66"}, "projectId": {}, "updatedAt": {"shape": "S5b"}, "userParameters": {"shape": "S5x"}}}}, "GetFormType": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/form-types/{formTypeIdentifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "formTypeIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "formTypeIdentifier": {"location": "uri", "locationName": "formTypeIdentifier"}, "revision": {"location": "querystring", "locationName": "revision"}}}, "output": {"type": "structure", "required": ["domainId", "model", "name", "revision"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "Sq"}, "domainId": {}, "imports": {"shape": "<PERSON>"}, "model": {"shape": "S69"}, "name": {"shape": "S22"}, "originDomainId": {}, "originProjectId": {}, "owningProjectId": {}, "revision": {}, "status": {}}}}, "GetGlossary": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/glossaries/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["domainId", "id", "name", "owningProjectId", "status"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "S6e"}, "domainId": {}, "id": {}, "name": {"shape": "S6f"}, "owningProjectId": {}, "status": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "GetGlossaryTerm": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/glossary-terms/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["domainId", "glossaryId", "id", "name", "status"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "glossaryId": {}, "id": {}, "longDescription": {"shape": "S6k"}, "name": {"shape": "Sy"}, "shortDescription": {"shape": "Sz"}, "status": {}, "termRelations": {"shape": "S6m"}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "GetGroupProfile": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/group-profiles/{groupIdentifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "groupIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "groupIdentifier": {"location": "uri", "locationName": "groupIdentifier"}}}, "output": {"type": "structure", "members": {"domainId": {}, "groupName": {"shape": "S6t"}, "id": {}, "status": {}}}}, "GetIamPortalLoginUrl": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/get-portal-login-url", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}}}, "output": {"type": "structure", "required": ["userProfileId"], "members": {"authCodeUrl": {}, "userProfileId": {}}}}, "GetLineageNode": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/lineage/nodes/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "eventTimestamp": {"location": "querystring", "locationName": "timestamp", "type": "timestamp"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["domainId", "id", "typeName"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {}, "domainId": {}, "downstreamNodes": {"shape": "Sb9"}, "eventTimestamp": {"type": "timestamp"}, "formsOutput": {"shape": "S20"}, "id": {}, "name": {}, "sourceIdentifier": {}, "typeName": {}, "typeRevision": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}, "upstreamNodes": {"shape": "Sb9"}}}}, "GetListing": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/listings/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "listingRevision": {"location": "querystring", "locationName": "listingRevision"}}}, "output": {"type": "structure", "required": ["domainId", "id", "listingRevision"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "Sq"}, "domainId": {}, "id": {}, "item": {"type": "structure", "members": {"assetListing": {"type": "structure", "members": {"assetId": {}, "assetRevision": {}, "assetType": {}, "createdAt": {"type": "timestamp"}, "forms": {}, "glossaryTerms": {"shape": "Sw"}, "latestTimeSeriesDataPointForms": {"shape": "S23"}, "owningProjectId": {}}}, "dataProductListing": {"type": "structure", "members": {"createdAt": {"type": "timestamp"}, "dataProductId": {}, "dataProductRevision": {}, "forms": {}, "glossaryTerms": {"shape": "Sw"}, "items": {"type": "list", "member": {"type": "structure", "members": {"glossaryTerms": {"shape": "Sw"}, "listingId": {}, "listingRevision": {}}}}, "owningProjectId": {}}}}, "union": true}, "listingRevision": {}, "name": {}, "status": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "GetMetadataGenerationRun": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/metadata-generation-runs/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["domainId", "id", "owningProjectId"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "id": {}, "owningProjectId": {}, "status": {}, "target": {"shape": "Sbm"}, "type": {}}}}, "GetProject": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/projects/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["created<PERSON>y", "domainId", "id", "name"], "members": {"createdAt": {"shape": "S5b"}, "createdBy": {}, "description": {"shape": "Sq"}, "domainId": {}, "failureReasons": {"shape": "S72"}, "glossaryTerms": {"shape": "S1t"}, "id": {}, "lastUpdatedAt": {"shape": "S5b"}, "name": {"shape": "S18"}, "projectStatus": {}}}}, "GetSubscription": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/subscriptions/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "id", "status", "subscribedListing", "subscribedPrincipal", "updatedAt"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "id": {}, "retainPermissions": {"type": "boolean"}, "status": {}, "subscribedListing": {"shape": "Sp"}, "subscribedPrincipal": {"shape": "S16"}, "subscriptionRequestId": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "GetSubscriptionGrant": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/subscription-grants/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "grantedEntity", "id", "status", "subscriptionTargetId", "updatedAt"], "members": {"assets": {"shape": "S7g"}, "createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "grantedEntity": {"shape": "S7k"}, "id": {}, "status": {}, "subscriptionId": {"deprecated": true, "deprecatedMessage": "Multiple subscriptions can exist for a single grant"}, "subscriptionTargetId": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "GetSubscriptionRequestDetails": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/subscription-requests/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "id", "requestReason", "status", "subscribedListings", "subscribedPrincipals", "updatedAt"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "decisionComment": {"shape": "Sh"}, "domainId": {}, "id": {}, "requestReason": {"shape": "Sm"}, "reviewerId": {}, "status": {}, "subscribedListings": {"type": "list", "member": {"shape": "Sp"}}, "subscribedPrincipals": {"type": "list", "member": {"shape": "S16"}}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "GetSubscriptionTarget": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/subscription-targets/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {"location": "uri", "locationName": "environmentIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["applicableAssetTypes", "authorizedPrincipals", "createdAt", "created<PERSON>y", "domainId", "environmentId", "id", "manageAccessRole", "name", "projectId", "provider", "subscriptionTargetConfig", "type"], "members": {"applicableAssetTypes": {"shape": "S7y"}, "authorizedPrincipals": {"shape": "S7z"}, "createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "environmentId": {}, "id": {}, "manageAccessRole": {}, "name": {"shape": "S81"}, "projectId": {}, "provider": {}, "subscriptionTargetConfig": {"shape": "S82"}, "type": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "GetTimeSeriesDataPoint": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/entities/{entityType}/{entityIdentifier}/time-series-data-points/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "entityIdentifier", "entityType", "formName", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "entityIdentifier": {"location": "uri", "locationName": "entityIdentifier"}, "entityType": {"location": "uri", "locationName": "entityType"}, "formName": {"location": "querystring", "locationName": "formName"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "members": {"domainId": {}, "entityId": {}, "entityType": {}, "form": {"shape": "Sc5"}, "formName": {}}}}, "GetUserProfile": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/user-profiles/{userIdentifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "userIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "type": {"location": "querystring", "locationName": "type"}, "userIdentifier": {"location": "uri", "locationName": "userIdentifier"}}}, "output": {"type": "structure", "members": {"details": {"shape": "S89"}, "domainId": {}, "id": {}, "status": {}, "type": {}}}}, "ListAssetFilters": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/assets/{assetIdentifier}/filters", "responseCode": 200}, "input": {"type": "structure", "required": ["assetIdentifier", "domainIdentifier"], "members": {"assetIdentifier": {"location": "uri", "locationName": "assetIdentifier"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "status": {"location": "querystring", "locationName": "status"}}}, "output": {"type": "structure", "required": ["items"], "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["assetId", "domainId", "id", "name"], "members": {"assetId": {}, "createdAt": {"type": "timestamp"}, "description": {"shape": "Sq"}, "domainId": {}, "effectiveColumnNames": {"shape": "S2e"}, "effectiveRowFilter": {}, "errorMessage": {}, "id": {}, "name": {"shape": "S2w"}, "status": {}}}}, "nextToken": {}}}}, "ListAssetRevisions": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/assets/{identifier}/revisions", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "members": {"items": {"type": "list", "member": {"type": "structure", "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "id": {}, "revision": {}}}}, "nextToken": {}}}}, "ListDataProductRevisions": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/data-products/{identifier}/revisions", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "required": ["items"], "members": {"items": {"type": "list", "member": {"type": "structure", "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "id": {}, "revision": {}}}}, "nextToken": {}}}}, "ListDataSourceRunActivities": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/data-source-runs/{identifier}/activities", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "status": {"location": "querystring", "locationName": "status"}}}, "output": {"type": "structure", "required": ["items"], "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["createdAt", "dataAssetStatus", "dataSourceRunId", "database", "projectId", "technicalName", "updatedAt"], "members": {"createdAt": {"shape": "S4n"}, "dataAssetId": {}, "dataAssetStatus": {}, "dataSourceRunId": {}, "database": {"shape": "S47"}, "errorMessage": {"shape": "S4o"}, "projectId": {}, "technicalDescription": {"shape": "Sq"}, "technicalName": {"shape": "S47"}, "updatedAt": {"shape": "S4n"}}}}, "nextToken": {}}}}, "ListDataSourceRuns": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/data-sources/{dataSourceIdentifier}/runs", "responseCode": 200}, "input": {"type": "structure", "required": ["dataSourceIdentifier", "domainIdentifier"], "members": {"dataSourceIdentifier": {"location": "uri", "locationName": "dataSourceIdentifier"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "status": {"location": "querystring", "locationName": "status"}}}, "output": {"type": "structure", "required": ["items"], "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["createdAt", "dataSourceId", "id", "projectId", "status", "type", "updatedAt"], "members": {"createdAt": {"shape": "S4n"}, "dataSourceId": {}, "errorMessage": {"shape": "S4o"}, "id": {}, "projectId": {}, "runStatisticsForAssets": {"shape": "Sa4"}, "startedAt": {"shape": "S4n"}, "status": {}, "stoppedAt": {"shape": "S4n"}, "type": {}, "updatedAt": {"shape": "S4n"}}}}, "nextToken": {}}}}, "ListDataSources": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/data-sources", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "projectIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {"location": "querystring", "locationName": "environmentIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "name": {"shape": "S47", "location": "querystring", "locationName": "name"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "projectIdentifier": {"location": "querystring", "locationName": "projectIdentifier"}, "status": {"location": "querystring", "locationName": "status"}, "type": {"location": "querystring", "locationName": "type"}}}, "output": {"type": "structure", "required": ["items"], "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["dataSourceId", "domainId", "environmentId", "name", "status", "type"], "members": {"createdAt": {"shape": "S4n"}, "dataSourceId": {}, "domainId": {}, "enableSetting": {}, "environmentId": {}, "lastRunAssetCount": {"type": "integer"}, "lastRunAt": {"shape": "S4n"}, "lastRunErrorMessage": {"shape": "S4o"}, "lastRunStatus": {}, "name": {"shape": "S47"}, "schedule": {"shape": "S49"}, "status": {}, "type": {}, "updatedAt": {"shape": "S4n"}}}}, "nextToken": {}}}}, "ListDomains": {"http": {"method": "GET", "requestUri": "/v2/domains", "responseCode": 200}, "input": {"type": "structure", "members": {"maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "status": {"location": "querystring", "locationName": "status"}}}, "output": {"type": "structure", "required": ["items"], "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["arn", "createdAt", "id", "managedAccountId", "name", "status"], "members": {"arn": {}, "createdAt": {"type": "timestamp"}, "description": {"type": "string", "sensitive": true}, "id": {}, "lastUpdatedAt": {"type": "timestamp"}, "managedAccountId": {}, "name": {"type": "string", "sensitive": true}, "portalUrl": {}, "status": {}}}}, "nextToken": {}}}}, "ListEnvironmentActions": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/actions", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {"location": "uri", "locationName": "environmentIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["domainId", "environmentId", "id", "name", "parameters"], "members": {"description": {}, "domainId": {}, "environmentId": {}, "id": {}, "name": {}, "parameters": {"shape": "S61"}}}}, "nextToken": {}}}}, "ListEnvironmentBlueprintConfigurations": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/environment-blueprint-configurations", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["domainId", "environmentBlueprintId"], "members": {"createdAt": {"shape": "S5b"}, "domainId": {}, "enabledRegions": {"shape": "Sah"}, "environmentBlueprintId": {}, "manageAccessRoleArn": {}, "provisioningConfigurations": {"shape": "<PERSON><PERSON>"}, "provisioningRoleArn": {}, "regionalParameters": {"shape": "Sao"}, "updatedAt": {"shape": "S5b"}}}}, "nextToken": {}}}}, "ListEnvironmentBlueprints": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/environment-blueprints", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "managed": {"location": "querystring", "locationName": "managed", "type": "boolean"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "name": {"location": "querystring", "locationName": "name"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "required": ["items"], "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["id", "name", "provider", "provisioningProperties"], "members": {"createdAt": {"shape": "S5b"}, "description": {"shape": "Sq"}, "id": {}, "name": {}, "provider": {}, "provisioningProperties": {"shape": "S5u"}, "updatedAt": {"shape": "S5b"}}}}, "nextToken": {}}}}, "ListEnvironmentProfiles": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/environment-profiles", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier"], "members": {"awsAccountId": {"location": "querystring", "locationName": "awsAccountId"}, "awsAccountRegion": {"location": "querystring", "locationName": "awsAccountRegion"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentBlueprintIdentifier": {"location": "querystring", "locationName": "environmentBlueprintIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "name": {"shape": "S66", "location": "querystring", "locationName": "name"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "projectIdentifier": {"location": "querystring", "locationName": "projectIdentifier"}}}, "output": {"type": "structure", "required": ["items"], "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["created<PERSON>y", "domainId", "environmentBlueprintId", "id", "name"], "members": {"awsAccountId": {}, "awsAccountRegion": {}, "createdAt": {"shape": "S5b"}, "createdBy": {}, "description": {"shape": "Sq"}, "domainId": {}, "environmentBlueprintId": {}, "id": {}, "name": {"shape": "S66"}, "projectId": {}, "updatedAt": {"shape": "S5b"}}}}, "nextToken": {}}}}, "ListEnvironments": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/environments", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "projectIdentifier"], "members": {"awsAccountId": {"location": "querystring", "locationName": "awsAccountId"}, "awsAccountRegion": {"location": "querystring", "locationName": "awsAccountRegion"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentBlueprintIdentifier": {"location": "querystring", "locationName": "environmentBlueprintIdentifier"}, "environmentProfileIdentifier": {"location": "querystring", "locationName": "environmentProfileIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "name": {"location": "querystring", "locationName": "name"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "projectIdentifier": {"location": "querystring", "locationName": "projectIdentifier"}, "provider": {"location": "querystring", "locationName": "provider"}, "status": {"location": "querystring", "locationName": "status"}}}, "output": {"type": "structure", "required": ["items"], "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["created<PERSON>y", "domainId", "name", "projectId", "provider"], "members": {"awsAccountId": {}, "awsAccountRegion": {}, "createdAt": {"shape": "S5b"}, "createdBy": {}, "description": {"shape": "Sq"}, "domainId": {}, "environmentProfileId": {}, "id": {}, "name": {"shape": "S5r"}, "projectId": {}, "provider": {}, "status": {}, "updatedAt": {"shape": "S5b"}}}}, "nextToken": {}}}}, "ListLineageNodeHistory": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/lineage/nodes/{identifier}/history", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"direction": {"location": "querystring", "locationName": "direction"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "eventTimestampGTE": {"location": "querystring", "locationName": "timestampGTE", "type": "timestamp"}, "eventTimestampLTE": {"location": "querystring", "locationName": "timestampLTE", "type": "timestamp"}, "identifier": {"location": "uri", "locationName": "identifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "sortOrder": {"location": "querystring", "locationName": "sortOrder"}}}, "output": {"type": "structure", "members": {"nextToken": {}, "nodes": {"type": "list", "member": {"type": "structure", "required": ["domainId", "id", "typeName"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {}, "domainId": {}, "eventTimestamp": {"type": "timestamp"}, "id": {}, "name": {}, "sourceIdentifier": {}, "typeName": {}, "typeRevision": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}}}}, "ListMetadataGenerationRuns": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/metadata-generation-runs", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "status": {"location": "querystring", "locationName": "status"}, "type": {"location": "querystring", "locationName": "type"}}}, "output": {"type": "structure", "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["domainId", "id", "owningProjectId"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "id": {}, "owningProjectId": {}, "status": {}, "target": {"shape": "Sbm"}, "type": {}}}}, "nextToken": {}}}}, "ListNotifications": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/notifications", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "type"], "members": {"afterTimestamp": {"location": "querystring", "locationName": "afterTimestamp", "type": "timestamp"}, "beforeTimestamp": {"location": "querystring", "locationName": "beforeTimestamp", "type": "timestamp"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "subjects": {"location": "querystring", "locationName": "subjects", "type": "list", "member": {}}, "taskStatus": {"location": "querystring", "locationName": "taskStatus"}, "type": {"location": "querystring", "locationName": "type"}}}, "output": {"type": "structure", "members": {"nextToken": {}, "notifications": {"type": "list", "member": {"type": "structure", "required": ["actionLink", "creationTimestamp", "domainIdentifier", "identifier", "lastUpdatedTimestamp", "message", "title", "topic", "type"], "members": {"actionLink": {"type": "string", "sensitive": true}, "creationTimestamp": {"type": "timestamp"}, "domainIdentifier": {}, "identifier": {}, "lastUpdatedTimestamp": {"type": "timestamp"}, "message": {"type": "string", "sensitive": true}, "metadata": {"type": "map", "key": {}, "value": {}}, "status": {}, "title": {"type": "string", "sensitive": true}, "topic": {"type": "structure", "required": ["resource", "role", "subject"], "members": {"resource": {"type": "structure", "required": ["id", "type"], "members": {"id": {}, "name": {}, "type": {}}}, "role": {}, "subject": {}}}, "type": {}}}}}}}, "ListProjectMemberships": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/projects/{projectIdentifier}/memberships", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "projectIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "projectIdentifier": {"location": "uri", "locationName": "projectIdentifier"}, "sortBy": {"location": "querystring", "locationName": "sortBy"}, "sortOrder": {"location": "querystring", "locationName": "sortOrder"}}}, "output": {"type": "structure", "required": ["members"], "members": {"members": {"type": "list", "member": {"type": "structure", "required": ["designation", "memberDetails"], "members": {"designation": {}, "memberDetails": {"type": "structure", "members": {"group": {"type": "structure", "required": ["groupId"], "members": {"groupId": {}}}, "user": {"type": "structure", "required": ["userId"], "members": {"userId": {}}}}, "union": true}}}}, "nextToken": {}}}}, "ListProjects": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/projects", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "groupIdentifier": {"location": "querystring", "locationName": "groupIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "name": {"shape": "S18", "location": "querystring", "locationName": "name"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "userIdentifier": {"location": "querystring", "locationName": "userIdentifier"}}}, "output": {"type": "structure", "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["created<PERSON>y", "domainId", "id", "name"], "members": {"createdAt": {"shape": "S5b"}, "createdBy": {}, "description": {"shape": "Sq"}, "domainId": {}, "failureReasons": {"shape": "S72"}, "id": {}, "name": {"shape": "S18"}, "projectStatus": {}, "updatedAt": {"shape": "S5b"}}}}, "nextToken": {}}}}, "ListSubscriptionGrants": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/subscription-grants", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentId": {"location": "querystring", "locationName": "environmentId"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "owningProjectId": {"location": "querystring", "locationName": "owningProjectId"}, "sortBy": {"location": "querystring", "locationName": "sortBy"}, "sortOrder": {"location": "querystring", "locationName": "sortOrder"}, "subscribedListingId": {"location": "querystring", "locationName": "subscribedListingId"}, "subscriptionId": {"location": "querystring", "locationName": "subscriptionId"}, "subscriptionTargetId": {"location": "querystring", "locationName": "subscriptionTargetId"}}}, "output": {"type": "structure", "required": ["items"], "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "grantedEntity", "id", "status", "subscriptionTargetId", "updatedAt"], "members": {"assets": {"shape": "S7g"}, "createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "grantedEntity": {"shape": "S7k"}, "id": {}, "status": {}, "subscriptionId": {"deprecated": true, "deprecatedMessage": "Multiple subscriptions can exist for a single grant"}, "subscriptionTargetId": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "nextToken": {}}}}, "ListSubscriptionRequests": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/subscription-requests", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier"], "members": {"approverProjectId": {"location": "querystring", "locationName": "approverProjectId"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "owningProjectId": {"location": "querystring", "locationName": "owningProjectId"}, "sortBy": {"location": "querystring", "locationName": "sortBy"}, "sortOrder": {"location": "querystring", "locationName": "sortOrder"}, "status": {"location": "querystring", "locationName": "status"}, "subscribedListingId": {"location": "querystring", "locationName": "subscribedListingId"}}}, "output": {"type": "structure", "required": ["items"], "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "id", "requestReason", "status", "subscribedListings", "subscribedPrincipals", "updatedAt"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "decisionComment": {"shape": "Sh"}, "domainId": {}, "id": {}, "requestReason": {"shape": "Sm"}, "reviewerId": {}, "status": {}, "subscribedListings": {"type": "list", "member": {"shape": "Sp"}}, "subscribedPrincipals": {"type": "list", "member": {"shape": "S16"}}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "nextToken": {}}}}, "ListSubscriptionTargets": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/subscription-targets", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {"location": "uri", "locationName": "environmentIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "sortBy": {"location": "querystring", "locationName": "sortBy"}, "sortOrder": {"location": "querystring", "locationName": "sortOrder"}}}, "output": {"type": "structure", "required": ["items"], "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["applicableAssetTypes", "authorizedPrincipals", "createdAt", "created<PERSON>y", "domainId", "environmentId", "id", "manageAccessRole", "name", "projectId", "provider", "subscriptionTargetConfig", "type"], "members": {"applicableAssetTypes": {"shape": "S7y"}, "authorizedPrincipals": {"shape": "S7z"}, "createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "environmentId": {}, "id": {}, "manageAccessRole": {}, "name": {"shape": "S81"}, "projectId": {}, "provider": {}, "subscriptionTargetConfig": {"shape": "S82"}, "type": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "nextToken": {}}}}, "ListSubscriptions": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/subscriptions", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier"], "members": {"approverProjectId": {"location": "querystring", "locationName": "approverProjectId"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "owningProjectId": {"location": "querystring", "locationName": "owningProjectId"}, "sortBy": {"location": "querystring", "locationName": "sortBy"}, "sortOrder": {"location": "querystring", "locationName": "sortOrder"}, "status": {"location": "querystring", "locationName": "status"}, "subscribedListingId": {"location": "querystring", "locationName": "subscribedListingId"}, "subscriptionRequestIdentifier": {"location": "querystring", "locationName": "subscriptionRequestIdentifier"}}}, "output": {"type": "structure", "required": ["items"], "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "id", "status", "subscribedListing", "subscribedPrincipal", "updatedAt"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "id": {}, "retainPermissions": {"type": "boolean"}, "status": {}, "subscribedListing": {"shape": "Sp"}, "subscribedPrincipal": {"shape": "S16"}, "subscriptionRequestId": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "nextToken": {}}}}, "ListTagsForResource": {"http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"location": "uri", "locationName": "resourceArn"}}}, "output": {"type": "structure", "members": {"tags": {"shape": "S4z"}}}}, "ListTimeSeriesDataPoints": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/entities/{entityType}/{entityIdentifier}/time-series-data-points", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "entityIdentifier", "entityType", "formName"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "endedAt": {"location": "querystring", "locationName": "endedAt", "type": "timestamp"}, "entityIdentifier": {"location": "uri", "locationName": "entityIdentifier"}, "entityType": {"location": "uri", "locationName": "entityType"}, "formName": {"location": "querystring", "locationName": "formName"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "startedAt": {"location": "querystring", "locationName": "startedAt", "type": "timestamp"}}}, "output": {"type": "structure", "members": {"items": {"shape": "S23"}, "nextToken": {}}}}, "PostLineageEvent": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/lineage/events", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "event"], "members": {"clientToken": {"idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "event": {"type": "blob", "sensitive": true}}, "payload": "event"}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "PostTimeSeriesDataPoints": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/entities/{entityType}/{entityIdentifier}/time-series-data-points", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "entityIdentifier", "entityType", "forms"], "members": {"clientToken": {"idempotencyToken": true}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "entityIdentifier": {"location": "uri", "locationName": "entityIdentifier"}, "entityType": {"location": "uri", "locationName": "entityType"}, "forms": {"type": "list", "member": {"type": "structure", "required": ["formName", "timestamp", "typeIdentifier"], "members": {"content": {}, "formName": {}, "timestamp": {"type": "timestamp"}, "typeIdentifier": {}, "typeRevision": {}}}}}}, "output": {"type": "structure", "members": {"domainId": {}, "entityId": {}, "entityType": {}, "forms": {"type": "list", "member": {"shape": "Sc5"}}}}, "idempotent": true}, "PutEnvironmentBlueprintConfiguration": {"http": {"method": "PUT", "requestUri": "/v2/domains/{domainIdentifier}/environment-blueprint-configurations/{environmentBlueprintIdentifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "enabledRegions", "environmentBlueprintIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "enabledRegions": {"shape": "Sah"}, "environmentBlueprintIdentifier": {"location": "uri", "locationName": "environmentBlueprintIdentifier"}, "manageAccessRoleArn": {}, "provisioningConfigurations": {"shape": "<PERSON><PERSON>"}, "provisioningRoleArn": {}, "regionalParameters": {"shape": "Sao"}}}, "output": {"type": "structure", "required": ["domainId", "environmentBlueprintId"], "members": {"createdAt": {"shape": "S5b"}, "domainId": {}, "enabledRegions": {"shape": "Sah"}, "environmentBlueprintId": {}, "manageAccessRoleArn": {}, "provisioningConfigurations": {"shape": "<PERSON><PERSON>"}, "provisioningRoleArn": {}, "regionalParameters": {"shape": "Sao"}, "updatedAt": {"shape": "S5b"}}}, "idempotent": true}, "RejectPredictions": {"http": {"method": "PUT", "requestUri": "/v2/domains/{domainIdentifier}/assets/{identifier}/reject-predictions", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"clientToken": {"idempotencyToken": true}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "rejectChoices": {"type": "list", "member": {"type": "structure", "required": ["predictionTarget"], "members": {"predictionChoices": {"type": "list", "member": {"type": "integer"}}, "predictionTarget": {}}}}, "rejectRule": {"type": "structure", "members": {"rule": {}, "threshold": {"type": "float"}}}, "revision": {"location": "querystring", "locationName": "revision"}}}, "output": {"type": "structure", "required": ["assetId", "assetRevision", "domainId"], "members": {"assetId": {}, "assetRevision": {}, "domainId": {}}}, "idempotent": true}, "RejectSubscriptionRequest": {"http": {"method": "PUT", "requestUri": "/v2/domains/{domainIdentifier}/subscription-requests/{identifier}/reject", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"decisionComment": {"shape": "Sh"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "id", "requestReason", "status", "subscribedListings", "subscribedPrincipals", "updatedAt"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "decisionComment": {"shape": "Sh"}, "domainId": {}, "id": {}, "requestReason": {"shape": "Sm"}, "reviewerId": {}, "status": {}, "subscribedListings": {"type": "list", "member": {"shape": "Sp"}}, "subscribedPrincipals": {"type": "list", "member": {"shape": "S16"}}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}, "idempotent": true}, "RevokeSubscription": {"http": {"method": "PUT", "requestUri": "/v2/domains/{domainIdentifier}/subscriptions/{identifier}/revoke", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "retainPermissions": {"type": "boolean"}}}, "output": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "id", "status", "subscribedListing", "subscribedPrincipal", "updatedAt"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "id": {}, "retainPermissions": {"type": "boolean"}, "status": {}, "subscribedListing": {"shape": "Sp"}, "subscribedPrincipal": {"shape": "S16"}, "subscriptionRequestId": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}, "idempotent": true}, "Search": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/search", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "searchScope"], "members": {"additionalAttributes": {"shape": "Sg5"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "filters": {"shape": "Sg7"}, "maxResults": {"type": "integer"}, "nextToken": {}, "owningProjectIdentifier": {}, "searchIn": {"shape": "Sgc"}, "searchScope": {}, "searchText": {}, "sort": {"shape": "Sgg"}}}, "output": {"type": "structure", "members": {"items": {"type": "list", "member": {"type": "structure", "members": {"assetItem": {"type": "structure", "required": ["domainId", "identifier", "name", "owningProjectId", "typeIdentifier", "typeRevision"], "members": {"additionalAttributes": {"type": "structure", "members": {"formsOutput": {"shape": "S20"}, "latestTimeSeriesDataPointFormsOutput": {"shape": "S23"}, "readOnlyFormsOutput": {"shape": "S20"}}}, "createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "Sq"}, "domainId": {}, "externalIdentifier": {"shape": "S1n"}, "firstRevisionCreatedAt": {"type": "timestamp"}, "firstRevisionCreatedBy": {}, "glossaryTerms": {"shape": "S1t"}, "identifier": {}, "name": {"shape": "S1v"}, "owningProjectId": {}, "typeIdentifier": {}, "typeRevision": {}}}, "dataProductItem": {"type": "structure", "required": ["domainId", "id", "name", "owningProjectId"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "S39"}, "domainId": {}, "firstRevisionCreatedAt": {"type": "timestamp"}, "firstRevisionCreatedBy": {}, "glossaryTerms": {"shape": "S1t"}, "id": {}, "name": {"shape": "S3f"}, "owningProjectId": {}}}, "glossaryItem": {"type": "structure", "required": ["domainId", "id", "name", "owningProjectId", "status"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "S6e"}, "domainId": {}, "id": {}, "name": {"shape": "S6f"}, "owningProjectId": {}, "status": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}, "glossaryTermItem": {"type": "structure", "required": ["domainId", "glossaryId", "id", "name", "status"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "glossaryId": {}, "id": {}, "longDescription": {"shape": "S6k"}, "name": {"shape": "Sy"}, "shortDescription": {"shape": "Sz"}, "status": {}, "termRelations": {"shape": "S6m"}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "union": true}}, "nextToken": {}, "totalMatchCount": {"type": "integer"}}}}, "SearchGroupProfiles": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/search-group-profiles", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "groupType"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "groupType": {}, "maxResults": {"type": "integer"}, "nextToken": {}, "searchText": {"type": "string", "sensitive": true}}}, "output": {"type": "structure", "members": {"items": {"type": "list", "member": {"type": "structure", "members": {"domainId": {}, "groupName": {"shape": "S6t"}, "id": {}, "status": {}}}}, "nextToken": {}}}}, "SearchListings": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/listings/search", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier"], "members": {"additionalAttributes": {"shape": "Sg5"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "filters": {"shape": "Sg7"}, "maxResults": {"type": "integer"}, "nextToken": {}, "searchIn": {"shape": "Sgc"}, "searchText": {}, "sort": {"shape": "Sgg"}}}, "output": {"type": "structure", "members": {"items": {"type": "list", "member": {"type": "structure", "members": {"assetListing": {"type": "structure", "members": {"additionalAttributes": {"type": "structure", "members": {"forms": {}, "latestTimeSeriesDataPointForms": {"shape": "S23"}}}, "createdAt": {"type": "timestamp"}, "description": {"shape": "Sq"}, "entityId": {}, "entityRevision": {}, "entityType": {}, "glossaryTerms": {"shape": "Sw"}, "listingCreatedBy": {}, "listingId": {}, "listingRevision": {}, "listingUpdatedBy": {}, "name": {"shape": "S1v"}, "owningProjectId": {}}}, "dataProductListing": {"type": "structure", "members": {"additionalAttributes": {"type": "structure", "members": {"forms": {}}}, "createdAt": {"type": "timestamp"}, "description": {"shape": "Sq"}, "entityId": {}, "entityRevision": {}, "glossaryTerms": {"shape": "Sw"}, "items": {"type": "list", "member": {"type": "structure", "members": {"glossaryTerms": {"shape": "Sw"}, "listingId": {}, "listingRevision": {}}}}, "listingCreatedBy": {}, "listingId": {}, "listingRevision": {}, "listingUpdatedBy": {}, "name": {"shape": "S3f"}, "owningProjectId": {}}}}, "union": true}}, "nextToken": {}, "totalMatchCount": {"type": "integer"}}}}, "SearchTypes": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/types-search", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "managed", "searchScope"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "filters": {"shape": "Sg7"}, "managed": {"type": "boolean"}, "maxResults": {"type": "integer"}, "nextToken": {}, "searchIn": {"shape": "Sgc"}, "searchScope": {}, "searchText": {}, "sort": {"shape": "Sgg"}}}, "output": {"type": "structure", "members": {"items": {"type": "list", "member": {"type": "structure", "members": {"assetTypeItem": {"type": "structure", "required": ["domainId", "formsOutput", "name", "owningProjectId", "revision"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "Sq"}, "domainId": {}, "formsOutput": {"shape": "S36"}, "name": {}, "originDomainId": {}, "originProjectId": {}, "owningProjectId": {}, "revision": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}, "formTypeItem": {"type": "structure", "required": ["domainId", "name", "revision"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "Sq"}, "domainId": {}, "imports": {"shape": "<PERSON>"}, "model": {"shape": "S69"}, "name": {"shape": "S22"}, "originDomainId": {}, "originProjectId": {}, "owningProjectId": {}, "revision": {}, "status": {}}}, "lineageNodeTypeItem": {"type": "structure", "required": ["domainId", "formsOutput", "revision"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {}, "domainId": {}, "formsOutput": {"shape": "S36"}, "name": {}, "revision": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "union": true}}, "nextToken": {}, "totalMatchCount": {"type": "integer"}}}}, "SearchUserProfiles": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/search-user-profiles", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "userType"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "maxResults": {"type": "integer"}, "nextToken": {}, "searchText": {"type": "string", "sensitive": true}, "userType": {}}}, "output": {"type": "structure", "members": {"items": {"type": "list", "member": {"type": "structure", "members": {"details": {"shape": "S89"}, "domainId": {}, "id": {}, "status": {}, "type": {}}}}, "nextToken": {}}}}, "StartDataSourceRun": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/data-sources/{dataSourceIdentifier}/runs", "responseCode": 200}, "input": {"type": "structure", "required": ["dataSourceIdentifier", "domainIdentifier"], "members": {"clientToken": {"idempotencyToken": true}, "dataSourceIdentifier": {"location": "uri", "locationName": "dataSourceIdentifier"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}}}, "output": {"type": "structure", "required": ["createdAt", "dataSourceId", "domainId", "id", "projectId", "status", "type", "updatedAt"], "members": {"createdAt": {"shape": "S4n"}, "dataSourceConfigurationSnapshot": {}, "dataSourceId": {}, "domainId": {}, "errorMessage": {"shape": "S4o"}, "id": {}, "projectId": {}, "runStatisticsForAssets": {"shape": "Sa4"}, "startedAt": {"shape": "S4n"}, "status": {}, "stoppedAt": {"shape": "S4n"}, "type": {}, "updatedAt": {"shape": "S4n"}}}, "idempotent": true}, "StartMetadataGenerationRun": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/metadata-generation-runs", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "owningProjectIdentifier", "target", "type"], "members": {"clientToken": {"idempotencyToken": true}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "owningProjectIdentifier": {}, "target": {"shape": "Sbm"}, "type": {}}}, "output": {"type": "structure", "required": ["domainId", "id"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "id": {}, "owningProjectId": {}, "status": {}, "type": {}}}, "idempotent": true}, "TagResource": {"http": {"requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "S4z"}}}, "output": {"type": "structure", "members": {}}}, "UntagResource": {"http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"location": "uri", "locationName": "resourceArn"}, "tagKeys": {"location": "querystring", "locationName": "tagKeys", "type": "list", "member": {}}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "UpdateAssetFilter": {"http": {"method": "PATCH", "requestUri": "/v2/domains/{domainIdentifier}/assets/{assetIdentifier}/filters/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["assetIdentifier", "domainIdentifier", "identifier"], "members": {"assetIdentifier": {"location": "uri", "locationName": "assetIdentifier"}, "configuration": {"shape": "S2c"}, "description": {"shape": "Sq"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "name": {}}}, "output": {"type": "structure", "required": ["assetId", "configuration", "domainId", "id", "name"], "members": {"assetId": {}, "configuration": {"shape": "S2c"}, "createdAt": {"type": "timestamp"}, "description": {"shape": "Sq"}, "domainId": {}, "effectiveColumnNames": {"shape": "S2e"}, "effectiveRowFilter": {}, "errorMessage": {}, "id": {}, "name": {"shape": "S2w"}, "status": {}}}, "idempotent": true}, "UpdateDataSource": {"http": {"method": "PATCH", "requestUri": "/v2/domains/{domainIdentifier}/data-sources/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"assetFormsInput": {"shape": "S1o"}, "configuration": {"shape": "S3m"}, "description": {"shape": "Sq"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "enableSetting": {}, "identifier": {"location": "uri", "locationName": "identifier"}, "name": {"shape": "S47"}, "publishOnImport": {"type": "boolean"}, "recommendation": {"shape": "S48"}, "retainPermissionsOnRevokeFailure": {"type": "boolean"}, "schedule": {"shape": "S49"}}}, "output": {"type": "structure", "required": ["domainId", "environmentId", "id", "name", "projectId"], "members": {"assetFormsOutput": {"shape": "S20"}, "configuration": {"shape": "S4e"}, "createdAt": {"shape": "S4n"}, "description": {"shape": "Sq"}, "domainId": {}, "enableSetting": {}, "environmentId": {}, "errorMessage": {"shape": "S4o"}, "id": {}, "lastRunAt": {"shape": "S4n"}, "lastRunErrorMessage": {"shape": "S4o"}, "lastRunStatus": {}, "name": {"shape": "S47"}, "projectId": {}, "publishOnImport": {"type": "boolean"}, "recommendation": {"shape": "S48"}, "retainPermissionsOnRevokeFailure": {"type": "boolean"}, "schedule": {"shape": "S49"}, "selfGrantStatus": {"shape": "S8r"}, "status": {}, "type": {}, "updatedAt": {"shape": "S4n"}}}, "idempotent": true}, "UpdateDomain": {"http": {"method": "PUT", "requestUri": "/v2/domains/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["identifier"], "members": {"clientToken": {"idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}, "description": {}, "domainExecutionRole": {}, "identifier": {"location": "uri", "locationName": "identifier"}, "name": {}, "singleSignOn": {"shape": "S4w"}}}, "output": {"type": "structure", "required": ["id"], "members": {"description": {}, "domainExecutionRole": {}, "id": {}, "lastUpdatedAt": {"type": "timestamp"}, "name": {}, "singleSignOn": {"shape": "S4w"}}}, "idempotent": true}, "UpdateEnvironment": {"http": {"method": "PATCH", "requestUri": "/v2/domains/{domainIdentifier}/environments/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"description": {}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "glossaryTerms": {"shape": "S1t"}, "identifier": {"location": "uri", "locationName": "identifier"}, "name": {}}}, "output": {"type": "structure", "required": ["created<PERSON>y", "domainId", "name", "projectId", "provider"], "members": {"awsAccountId": {}, "awsAccountRegion": {}, "createdAt": {"shape": "S5b"}, "createdBy": {}, "deploymentProperties": {"shape": "S5c"}, "description": {"shape": "Sq"}, "domainId": {}, "environmentActions": {"shape": "S5f"}, "environmentBlueprintId": {}, "environmentProfileId": {}, "glossaryTerms": {"shape": "S1t"}, "id": {}, "lastDeployment": {"shape": "S5l"}, "name": {"shape": "S5r"}, "projectId": {}, "provider": {}, "provisionedResources": {"shape": "S5s"}, "provisioningProperties": {"shape": "S5u"}, "status": {}, "updatedAt": {"shape": "S5b"}, "userParameters": {"shape": "S5x"}}}}, "UpdateEnvironmentAction": {"http": {"method": "PATCH", "requestUri": "/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/actions/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentIdentifier", "identifier"], "members": {"description": {}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {"location": "uri", "locationName": "environmentIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "name": {}, "parameters": {"shape": "S61"}}}, "output": {"type": "structure", "required": ["domainId", "environmentId", "id", "name", "parameters"], "members": {"description": {}, "domainId": {}, "environmentId": {}, "id": {}, "name": {}, "parameters": {"shape": "S61"}}}}, "UpdateEnvironmentProfile": {"http": {"method": "PATCH", "requestUri": "/v2/domains/{domainIdentifier}/environment-profiles/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"awsAccountId": {}, "awsAccountRegion": {}, "description": {}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "name": {"shape": "S66"}, "userParameters": {"shape": "S56"}}}, "output": {"type": "structure", "required": ["created<PERSON>y", "domainId", "environmentBlueprintId", "id", "name"], "members": {"awsAccountId": {}, "awsAccountRegion": {}, "createdAt": {"shape": "S5b"}, "createdBy": {}, "description": {"shape": "Sq"}, "domainId": {}, "environmentBlueprintId": {}, "id": {}, "name": {"shape": "S66"}, "projectId": {}, "updatedAt": {"shape": "S5b"}, "userParameters": {"shape": "S5x"}}}}, "UpdateGlossary": {"http": {"method": "PATCH", "requestUri": "/v2/domains/{domainIdentifier}/glossaries/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"clientToken": {"idempotencyToken": true}, "description": {"shape": "S6e"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "name": {"shape": "S6f"}, "status": {}}}, "output": {"type": "structure", "required": ["domainId", "id", "name", "owningProjectId"], "members": {"description": {"shape": "S6e"}, "domainId": {}, "id": {}, "name": {"shape": "S6f"}, "owningProjectId": {}, "status": {}}}, "idempotent": true}, "UpdateGlossaryTerm": {"http": {"method": "PATCH", "requestUri": "/v2/domains/{domainIdentifier}/glossary-terms/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "glossaryIdentifier": {}, "identifier": {"location": "uri", "locationName": "identifier"}, "longDescription": {"shape": "S6k"}, "name": {"shape": "Sy"}, "shortDescription": {"shape": "Sz"}, "status": {}, "termRelations": {"shape": "S6m"}}}, "output": {"type": "structure", "required": ["domainId", "glossaryId", "id", "name", "status"], "members": {"domainId": {}, "glossaryId": {}, "id": {}, "longDescription": {"shape": "S6k"}, "name": {"shape": "Sy"}, "shortDescription": {"shape": "Sz"}, "status": {}, "termRelations": {"shape": "S6m"}}}, "idempotent": true}, "UpdateGroupProfile": {"http": {"method": "PUT", "requestUri": "/v2/domains/{domainIdentifier}/group-profiles/{groupIdentifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "groupIdentifier", "status"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "groupIdentifier": {"location": "uri", "locationName": "groupIdentifier"}, "status": {}}}, "output": {"type": "structure", "members": {"domainId": {}, "groupName": {"shape": "S6t"}, "id": {}, "status": {}}}}, "UpdateProject": {"http": {"method": "PATCH", "requestUri": "/v2/domains/{domainIdentifier}/projects/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"description": {"shape": "Sq"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "glossaryTerms": {"shape": "S1t"}, "identifier": {"location": "uri", "locationName": "identifier"}, "name": {"shape": "S18"}}}, "output": {"type": "structure", "required": ["created<PERSON>y", "domainId", "id", "name"], "members": {"createdAt": {"shape": "S5b"}, "createdBy": {}, "description": {"shape": "Sq"}, "domainId": {}, "failureReasons": {"shape": "S72"}, "glossaryTerms": {"shape": "S1t"}, "id": {}, "lastUpdatedAt": {"shape": "S5b"}, "name": {"shape": "S18"}, "projectStatus": {}}}, "idempotent": true}, "UpdateSubscriptionGrantStatus": {"http": {"method": "PATCH", "requestUri": "/v2/domains/{domainIdentifier}/subscription-grants/{identifier}/status/{assetIdentifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["assetIdentifier", "domainIdentifier", "identifier", "status"], "members": {"assetIdentifier": {"location": "uri", "locationName": "assetIdentifier"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "failureCause": {"shape": "S7i"}, "identifier": {"location": "uri", "locationName": "identifier"}, "status": {}, "targetName": {}}}, "output": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "grantedEntity", "id", "status", "subscriptionTargetId", "updatedAt"], "members": {"assets": {"shape": "S7g"}, "createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "grantedEntity": {"shape": "S7k"}, "id": {}, "status": {}, "subscriptionId": {"deprecated": true, "deprecatedMessage": "Multiple subscriptions can exist for a single grant"}, "subscriptionTargetId": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}, "idempotent": true}, "UpdateSubscriptionRequest": {"http": {"method": "PATCH", "requestUri": "/v2/domains/{domainIdentifier}/subscription-requests/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier", "requestReason"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "requestReason": {"shape": "Sm"}}}, "output": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "id", "requestReason", "status", "subscribedListings", "subscribedPrincipals", "updatedAt"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "decisionComment": {"shape": "Sh"}, "domainId": {}, "id": {}, "requestReason": {"shape": "Sm"}, "reviewerId": {}, "status": {}, "subscribedListings": {"type": "list", "member": {"shape": "Sp"}}, "subscribedPrincipals": {"type": "list", "member": {"shape": "S16"}}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}, "idempotent": true}, "UpdateSubscriptionTarget": {"http": {"method": "PATCH", "requestUri": "/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/subscription-targets/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentIdentifier", "identifier"], "members": {"applicableAssetTypes": {"shape": "S7y"}, "authorizedPrincipals": {"shape": "S7z"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {"location": "uri", "locationName": "environmentIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "manageAccessRole": {}, "name": {"shape": "S81"}, "provider": {}, "subscriptionTargetConfig": {"shape": "S82"}}}, "output": {"type": "structure", "required": ["applicableAssetTypes", "authorizedPrincipals", "createdAt", "created<PERSON>y", "domainId", "environmentId", "id", "manageAccessRole", "name", "projectId", "provider", "subscriptionTargetConfig", "type"], "members": {"applicableAssetTypes": {"shape": "S7y"}, "authorizedPrincipals": {"shape": "S7z"}, "createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "environmentId": {}, "id": {}, "manageAccessRole": {}, "name": {"shape": "S81"}, "projectId": {}, "provider": {}, "subscriptionTargetConfig": {"shape": "S82"}, "type": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}, "idempotent": true}, "UpdateUserProfile": {"http": {"method": "PUT", "requestUri": "/v2/domains/{domainIdentifier}/user-profiles/{userIdentifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "status", "userIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "status": {}, "type": {}, "userIdentifier": {"location": "uri", "locationName": "userIdentifier"}}}, "output": {"type": "structure", "members": {"details": {"shape": "S89"}, "domainId": {}, "id": {}, "status": {}, "type": {}}}}}, "shapes": {"Sh": {"type": "string", "sensitive": true}, "Sm": {"type": "string", "sensitive": true}, "Sp": {"type": "structure", "required": ["description", "id", "item", "name", "ownerProjectId"], "members": {"description": {"shape": "Sq"}, "id": {}, "item": {"type": "structure", "members": {"assetListing": {"type": "structure", "members": {"entityId": {}, "entityRevision": {}, "entityType": {}, "forms": {}, "glossaryTerms": {"shape": "Sw"}}}, "productListing": {"type": "structure", "members": {"assetListings": {"type": "list", "member": {"type": "structure", "members": {"entityId": {}, "entityRevision": {}, "entityType": {}}}}, "description": {}, "entityId": {}, "entityRevision": {}, "glossaryTerms": {"shape": "Sw"}, "name": {}}}}, "union": true}, "name": {}, "ownerProjectId": {}, "ownerProjectName": {}, "revision": {}}}, "Sq": {"type": "string", "sensitive": true}, "Sw": {"type": "list", "member": {"type": "structure", "members": {"name": {"shape": "Sy"}, "shortDescription": {"shape": "Sz"}}}}, "Sy": {"type": "string", "sensitive": true}, "Sz": {"type": "string", "sensitive": true}, "S16": {"type": "structure", "members": {"project": {"type": "structure", "members": {"id": {}, "name": {"shape": "S18"}}}}, "union": true}, "S18": {"type": "string", "sensitive": true}, "S1n": {"type": "string", "sensitive": true}, "S1o": {"type": "list", "member": {"type": "structure", "required": ["formName"], "members": {"content": {}, "formName": {}, "typeIdentifier": {}, "typeRevision": {}}, "sensitive": true}, "sensitive": true}, "S1t": {"type": "list", "member": {}}, "S1v": {"type": "string", "sensitive": true}, "S1w": {"type": "structure", "members": {"businessNameGeneration": {"type": "structure", "members": {"enabled": {"type": "boolean"}}}}}, "S20": {"type": "list", "member": {"type": "structure", "required": ["formName"], "members": {"content": {}, "formName": {}, "typeName": {"shape": "S22"}, "typeRevision": {}}}}, "S22": {"type": "string", "sensitive": true}, "S23": {"type": "list", "member": {"type": "structure", "required": ["formName", "timestamp", "typeIdentifier"], "members": {"contentSummary": {}, "formName": {}, "id": {}, "timestamp": {"type": "timestamp"}, "typeIdentifier": {}, "typeRevision": {}}}}, "S29": {"type": "structure", "required": ["listingId", "listingStatus"], "members": {"listingId": {}, "listingStatus": {}}}, "S2c": {"type": "structure", "members": {"columnConfiguration": {"type": "structure", "members": {"includedColumnNames": {"shape": "S2e"}}}, "rowConfiguration": {"type": "structure", "required": ["rowFilter"], "members": {"rowFilter": {"shape": "S2g"}, "sensitive": {"type": "boolean"}}}}, "union": true}, "S2e": {"type": "list", "member": {}}, "S2g": {"type": "structure", "members": {"and": {"shape": "S2h"}, "expression": {"type": "structure", "members": {"equalTo": {"type": "structure", "required": ["columnName", "value"], "members": {"columnName": {}, "value": {}}}, "greaterThan": {"type": "structure", "required": ["columnName", "value"], "members": {"columnName": {}, "value": {}}}, "greaterThanOrEqualTo": {"type": "structure", "required": ["columnName", "value"], "members": {"columnName": {}, "value": {}}}, "in": {"type": "structure", "required": ["columnName", "values"], "members": {"columnName": {}, "values": {"shape": "S2n"}}}, "isNotNull": {"type": "structure", "required": ["columnName"], "members": {"columnName": {}}}, "isNull": {"type": "structure", "required": ["columnName"], "members": {"columnName": {}}}, "lessThan": {"type": "structure", "required": ["columnName", "value"], "members": {"columnName": {}, "value": {}}}, "lessThanOrEqualTo": {"type": "structure", "required": ["columnName", "value"], "members": {"columnName": {}, "value": {}}}, "like": {"type": "structure", "required": ["columnName", "value"], "members": {"columnName": {}, "value": {}}}, "notEqualTo": {"type": "structure", "required": ["columnName", "value"], "members": {"columnName": {}, "value": {}}}, "notIn": {"type": "structure", "required": ["columnName", "values"], "members": {"columnName": {}, "values": {"shape": "S2n"}}}, "notLike": {"type": "structure", "required": ["columnName", "value"], "members": {"columnName": {}, "value": {}}}}, "union": true}, "or": {"shape": "S2h"}}, "union": true}, "S2h": {"type": "list", "member": {"shape": "S2g"}}, "S2n": {"type": "list", "member": {}}, "S2w": {"type": "string", "sensitive": true}, "S36": {"type": "map", "key": {}, "value": {"type": "structure", "required": ["typeName", "typeRevision"], "members": {"required": {"type": "boolean"}, "typeName": {"shape": "S22"}, "typeRevision": {}}}}, "S39": {"type": "string", "sensitive": true}, "S3a": {"type": "list", "member": {"type": "structure", "required": ["identifier", "itemType"], "members": {"glossaryTerms": {"type": "list", "member": {}}, "identifier": {}, "itemType": {}, "revision": {}}}}, "S3f": {"type": "string", "sensitive": true}, "S3m": {"type": "structure", "members": {"glueRunConfiguration": {"type": "structure", "required": ["relationalFilterConfigurations"], "members": {"autoImportDataQualityResult": {"type": "boolean"}, "dataAccessRole": {}, "relationalFilterConfigurations": {"shape": "S3p"}}}, "redshiftRunConfiguration": {"type": "structure", "required": ["redshiftCredentialConfiguration", "redshiftStorage", "relationalFilterConfigurations"], "members": {"dataAccessRole": {}, "redshiftCredentialConfiguration": {"shape": "S3z"}, "redshiftStorage": {"shape": "S41"}, "relationalFilterConfigurations": {"shape": "S3p"}}}}, "union": true}, "S3p": {"type": "list", "member": {"type": "structure", "required": ["databaseName"], "members": {"databaseName": {}, "filterExpressions": {"type": "list", "member": {"type": "structure", "required": ["expression", "type"], "members": {"expression": {}, "type": {}}}}, "schemaName": {}}}}, "S3z": {"type": "structure", "required": ["secretManagerArn"], "members": {"secretManagerArn": {}}}, "S41": {"type": "structure", "members": {"redshiftClusterSource": {"type": "structure", "required": ["clusterName"], "members": {"clusterName": {}}}, "redshiftServerlessSource": {"type": "structure", "required": ["workgroupName"], "members": {"workgroupName": {}}}}, "union": true}, "S47": {"type": "string", "sensitive": true}, "S48": {"type": "structure", "members": {"enableBusinessNameGeneration": {"type": "boolean"}}}, "S49": {"type": "structure", "members": {"schedule": {}, "timezone": {}}, "sensitive": true}, "S4e": {"type": "structure", "members": {"glueRunConfiguration": {"type": "structure", "required": ["relationalFilterConfigurations"], "members": {"accountId": {}, "autoImportDataQualityResult": {"type": "boolean"}, "dataAccessRole": {}, "region": {}, "relationalFilterConfigurations": {"shape": "S3p"}}}, "redshiftRunConfiguration": {"type": "structure", "required": ["redshiftCredentialConfiguration", "redshiftStorage", "relationalFilterConfigurations"], "members": {"accountId": {}, "dataAccessRole": {}, "redshiftCredentialConfiguration": {"shape": "S3z"}, "redshiftStorage": {"shape": "S41"}, "region": {}, "relationalFilterConfigurations": {"shape": "S3p"}}}}, "union": true}, "S4n": {"type": "timestamp", "timestampFormat": "iso8601"}, "S4o": {"type": "structure", "required": ["errorType"], "members": {"errorDetail": {}, "errorType": {}}}, "S4w": {"type": "structure", "members": {"type": {}, "userAssignment": {}}}, "S4z": {"type": "map", "key": {}, "value": {}}, "S56": {"type": "list", "member": {"type": "structure", "members": {"name": {}, "value": {}}}}, "S5b": {"type": "timestamp", "timestampFormat": "iso8601"}, "S5c": {"type": "structure", "members": {"endTimeoutMinutes": {"type": "integer"}, "startTimeoutMinutes": {"type": "integer"}}}, "S5f": {"type": "list", "member": {"type": "structure", "required": ["parameters", "type"], "members": {"auth": {}, "parameters": {"type": "list", "member": {"type": "structure", "members": {"key": {}, "value": {}}}}, "type": {}}}}, "S5l": {"type": "structure", "members": {"deploymentId": {}, "deploymentStatus": {}, "deploymentType": {}, "failureReason": {"type": "structure", "required": ["message"], "members": {"code": {}, "message": {}}}, "isDeploymentComplete": {"type": "boolean"}, "messages": {"type": "list", "member": {}}}}, "S5r": {"type": "string", "sensitive": true}, "S5s": {"type": "list", "member": {"type": "structure", "required": ["type", "value"], "members": {"name": {}, "provider": {}, "type": {}, "value": {}}}}, "S5u": {"type": "structure", "members": {"cloudFormation": {"type": "structure", "required": ["templateUrl"], "members": {"templateUrl": {}}}}, "union": true}, "S5x": {"type": "list", "member": {"type": "structure", "required": ["fieldType", "keyName"], "members": {"defaultValue": {}, "description": {"shape": "Sq"}, "fieldType": {}, "isEditable": {"type": "boolean"}, "isOptional": {"type": "boolean"}, "keyName": {}}}}, "S61": {"type": "structure", "members": {"awsConsoleLink": {"type": "structure", "members": {"uri": {}}}}, "union": true}, "S66": {"type": "string", "sensitive": true}, "S69": {"type": "structure", "members": {"smithy": {}}, "sensitive": true, "union": true}, "S6e": {"type": "string", "sensitive": true}, "S6f": {"type": "string", "sensitive": true}, "S6k": {"type": "string", "sensitive": true}, "S6m": {"type": "structure", "members": {"classifies": {"type": "list", "member": {}}, "isA": {"type": "list", "member": {}}}}, "S6t": {"type": "string", "sensitive": true}, "S72": {"type": "list", "member": {"type": "structure", "members": {"code": {}, "message": {}}}}, "S77": {"type": "structure", "members": {"groupIdentifier": {}, "userIdentifier": {}}, "union": true}, "S7g": {"type": "list", "member": {"type": "structure", "required": ["assetId", "assetRevision", "status"], "members": {"assetId": {}, "assetRevision": {}, "failureCause": {"shape": "S7i"}, "failureTimestamp": {"type": "timestamp"}, "grantedTimestamp": {"type": "timestamp"}, "status": {}, "targetName": {}}}}, "S7i": {"type": "structure", "members": {"message": {}}}, "S7k": {"type": "structure", "members": {"listing": {"type": "structure", "required": ["id", "revision"], "members": {"id": {}, "revision": {}}}}, "union": true}, "S7y": {"type": "list", "member": {}}, "S7z": {"type": "list", "member": {}}, "S81": {"type": "string", "sensitive": true}, "S82": {"type": "list", "member": {"type": "structure", "required": ["content", "formName"], "members": {"content": {}, "formName": {}}}}, "S89": {"type": "structure", "members": {"iam": {"type": "structure", "members": {"arn": {}}}, "sso": {"type": "structure", "members": {"firstName": {"type": "string", "sensitive": true}, "lastName": {"type": "string", "sensitive": true}, "username": {"type": "string", "sensitive": true}}}}, "union": true}, "S8r": {"type": "structure", "members": {"glueSelfGrantStatus": {"type": "structure", "required": ["selfGrantStatusDetails"], "members": {"selfGrantStatusDetails": {"shape": "S8t"}}}, "redshiftSelfGrantStatus": {"type": "structure", "required": ["selfGrantStatusDetails"], "members": {"selfGrantStatusDetails": {"shape": "S8t"}}}}, "union": true}, "S8t": {"type": "list", "member": {"type": "structure", "required": ["databaseName", "status"], "members": {"databaseName": {}, "failureCause": {}, "schemaName": {}, "status": {}}}}, "Sa4": {"type": "structure", "members": {"added": {"type": "integer"}, "failed": {"type": "integer"}, "skipped": {"type": "integer"}, "unchanged": {"type": "integer"}, "updated": {"type": "integer"}}}, "Sah": {"type": "list", "member": {}}, "Saj": {"type": "list", "member": {"type": "structure", "members": {"lakeFormationConfiguration": {"type": "structure", "members": {"locationRegistrationExcludeS3Locations": {"type": "list", "member": {}}, "locationRegistrationRole": {}}}}, "union": true}}, "Sao": {"type": "map", "key": {}, "value": {"type": "map", "key": {}, "value": {}}}, "Saw": {"type": "list", "member": {"type": "structure", "required": ["name", "revision"], "members": {"name": {"shape": "S22"}, "revision": {}}}}, "Sb9": {"type": "list", "member": {"type": "structure", "members": {"eventTimestamp": {"type": "timestamp"}, "id": {}}}}, "Sbm": {"type": "structure", "required": ["identifier", "type"], "members": {"identifier": {}, "revision": {}, "type": {}}}, "Sc5": {"type": "structure", "required": ["formName", "timestamp", "typeIdentifier"], "members": {"content": {}, "formName": {}, "id": {}, "timestamp": {"type": "timestamp"}, "typeIdentifier": {}, "typeRevision": {}}}, "Sg5": {"type": "list", "member": {}}, "Sg7": {"type": "structure", "members": {"and": {"shape": "Sg8"}, "filter": {"type": "structure", "required": ["attribute", "value"], "members": {"attribute": {}, "value": {}}}, "or": {"shape": "Sg8"}}, "union": true}, "Sg8": {"type": "list", "member": {"shape": "Sg7"}}, "Sgc": {"type": "list", "member": {"type": "structure", "required": ["attribute"], "members": {"attribute": {}}}}, "Sgg": {"type": "structure", "required": ["attribute"], "members": {"attribute": {}, "order": {}}}}}