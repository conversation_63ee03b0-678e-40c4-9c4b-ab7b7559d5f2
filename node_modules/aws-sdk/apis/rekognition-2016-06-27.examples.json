{"version": "1.0", "examples": {"AssociateFaces": [{"input": {"ClientRequestToken": "550e8400-e29b-41d4-a716-446655440002", "CollectionId": "MyCollection", "FaceIds": ["f5817d37-94f6-4335-bfee-6cf79a3d806e", "851cb847-dccc-4fea-9309-9f4805967855", "35ebbb41-7f67-4263-908d-dd0ecba05ab9"], "UserId": "DemoUser", "UserMatchThreshold": 70}, "output": {"AssociatedFaces": [{"FaceId": "35ebbb41-7f67-4263-908d-dd0ecba05ab9"}], "UnsuccessfulFaceAssociations": [{"Confidence": 0.9375374913215637, "FaceId": "f5817d37-94f6-4335-bfee-6cf79a3d806e", "Reasons": ["LOW_MATCH_CONFIDENCE"]}, {"FaceId": "851cb847-dccc-4fea-9309-9f4805967855", "Reasons": ["ASSOCIATED_TO_A_DIFFERENT_USER"], "UserId": "demoUser2"}], "UserStatus": "UPDATING"}, "comments": {"input": {}, "output": {}}, "description": "This operation associates one or more faces with an existing UserID.", "id": "associatefaces-1686181269281", "title": "AssociateFaces"}], "CompareFaces": [{"input": {"SimilarityThreshold": 90, "SourceImage": {"S3Object": {"Bucket": "mybucket", "Name": "mysourceimage"}}, "TargetImage": {"S3Object": {"Bucket": "mybucket", "Name": "mytargetimage"}}}, "output": {"FaceMatches": [{"Face": {"BoundingBox": {"Height": 0.33481481671333313, "Left": 0.31888890266418457, "Top": 0.4933333396911621, "Width": 0.25}, "Confidence": 99.9991226196289}, "Similarity": 100}], "SourceImageFace": {"BoundingBox": {"Height": 0.33481481671333313, "Left": 0.31888890266418457, "Top": 0.4933333396911621, "Width": 0.25}, "Confidence": 99.9991226196289}}, "comments": {"input": {}, "output": {}}, "description": "This operation compares the largest face detected in the source image with each face detected in the target image.", "id": "to-compare-two-images-1482181985581", "title": "To compare two images"}], "CopyProjectVersion": [{"input": {"DestinationProjectArn": "arn:aws:rekognition:us-east-1:************:project/DestinationProject/*************", "KmsKeyId": "arn:1234abcd-12ab-34cd-56ef-1234567890ab", "OutputConfig": {"S3Bucket": "bucket-name", "S3KeyPrefix": "path_to_folder"}, "SourceProjectArn": "arn:aws:rekognition:us-east-1:************:project/SourceProject/***********", "SourceProjectVersionArn": "arn:aws:rekognition:us-east-1:************:project/SourceProject/version/model_1/*************", "Tags": {"key1": "val1"}, "VersionName": "DestinationVersionName_cross_account"}, "output": {"ProjectVersionArn": "arn:aws:rekognition:us-east-1:************:project/DestinationProject/version/DestinationVersionName_cross_account/*************1"}, "comments": {"input": {}, "output": {}}, "description": "Copies a version of an Amazon Rekognition Custom Labels model from a source project to a destination project.", "id": "copyprojectversion-*************", "title": "CopyProjectVersion"}], "CreateCollection": [{"input": {"CollectionId": "myphotos"}, "output": {"CollectionArn": "aws:rekognition:us-west-2:************:collection/myphotos", "StatusCode": 200}, "comments": {"input": {}, "output": {}}, "description": "This operation creates a Rekognition collection for storing image data.", "id": "to-create-a-collection-*************", "title": "To create a collection"}], "CreateDataset": [{"input": {"DatasetSource": {"GroundTruthManifest": {"S3Object": {"Bucket": "my-bucket", "Name": "datasets/flowers_training/manifests/output/output.manifest"}}}, "DatasetType": "TRAIN", "ProjectArn": "arn:aws:rekognition:us-east-1:************:project/my-project/*************"}, "output": {"DatasetArn": "arn:aws:rekognition:us-east-1:************:project/my-project/dataset/train/1690476084535"}, "comments": {"input": {}, "output": {}}, "description": "Creates an Amazon Rekognition Custom Labels dataset with a manifest file stored in an Amazon S3 bucket.", "id": "to-create-an-amazon-rekognition-custom-labels-dataset-1690476237145", "title": "To create an Amazon Rekognition Custom Labels dataset"}], "CreateProject": [{"input": {"ProjectName": "my-project"}, "output": {"ProjectArn": "arn:aws:rekognition:us-east-1:************:project/my-project/1690405809285"}, "comments": {"input": {}, "output": {}}, "description": "Creates an Amazon Rekognition Custom Labels project.", "id": "to-create-an-amazon-rekognition-custom-labels-project-1690405898597", "title": "To create an Amazon Rekognition Custom Labels project"}], "CreateProjectVersion": [{"input": {"OutputConfig": {"S3Bucket": "output_bucket", "S3KeyPrefix": "output_folder"}, "ProjectArn": "arn:aws:rekognition:us-east-1:************:project/my-project/*************", "VersionName": "1"}, "output": {"ProjectVersionArn": "arn:aws:rekognition:us-east-1:************:project/my-project/version/1/1690556751958"}, "comments": {"input": {}}, "description": "Trains a version of an Amazon Rekognition Custom Labels model.", "id": "to-train-an-amazon-rekognition-custom-labels-model-1690555832843", "title": "To train an Amazon Rekognition Custom Labels model"}], "CreateUser": [{"input": {"CollectionId": "MyCollection", "UserId": "DemoUser"}, "comments": {"input": {}, "output": {}}, "description": "Creates a new User within a collection specified by CollectionId.", "id": "createuser-1686181562299", "title": "CreateUser"}], "DeleteCollection": [{"input": {"CollectionId": "myphotos"}, "output": {"StatusCode": 200}, "comments": {"input": {}, "output": {}}, "description": "This operation deletes a Rekognition collection.", "id": "to-delete-a-collection-1481838179973", "title": "To delete a collection"}], "DeleteDataset": [{"input": {"DatasetArn": "arn:aws:rekognition:us-east-1:************:project/my-project/dataset/test/1690556733321"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "Deletes an Amazon Rekognition Custom Labels dataset.", "id": "to-delete-an-amazon-rekognition-custom-labels-dataset-1690826556349", "title": "To delete an Amazon Rekognition Custom Labels dataset"}], "DeleteFaces": [{"input": {"CollectionId": "myphotos", "FaceIds": ["ff43d742-0c13-5d16-a3e8-03d3f58e980b"]}, "output": {"DeletedFaces": ["ff43d742-0c13-5d16-a3e8-03d3f58e980b"]}, "comments": {"input": {}, "output": {}}, "description": "This operation deletes one or more faces from a Rekognition collection.", "id": "to-delete-a-face-1482182799377", "title": "To delete a face"}], "DeleteProject": [{"input": {"ProjectArn": "arn:aws:rekognition:us-east-1:************:project/my-project/1690405809285"}, "output": {"Status": "DELETING"}, "comments": {"input": {}, "output": {}}, "description": "Deletes an Amazon Rekognition Custom Labels projects.", "id": "to-delete-an-amazon-rekognition-custom-labels-project-1690409093478", "title": "To delete an Amazon Rekognition Custom Labels project"}], "DeleteProjectPolicy": [{"input": {"PolicyName": "testPolicy1", "PolicyRevisionId": "3b274c25e9203a56a99e00e3ff205fbc", "ProjectArn": "arn:aws:rekognition:us-east-1:************:project/SourceProject/1656557123456"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This operation deletes a revision of an existing project policy from an Amazon Rekognition Custom Labels project.", "id": "deleteprojectpolicy-1658204413810", "title": "DeleteProjectPolicy"}], "DeleteProjectVersion": [{"input": {"ProjectVersionArn": "arn:aws:rekognition:us-east-1:************:project/my-project/version/1/1690556751958"}, "output": {"Status": "DELETING"}, "comments": {"input": {}, "output": {}}, "description": "Deletes a version of an Amazon Rekognition Custom Labels model.", "id": "to-delete-an-amazon-rekognition-custom-labels-model-1690827562055", "title": "To delete an Amazon Rekognition Custom Labels model"}], "DeleteUser": [{"input": {"ClientRequestToken": "550e8400-e29b-41d4-a716-446655440001", "CollectionId": "MyCollection", "UserId": "DemoUser"}, "comments": {"input": {}, "output": {}}, "description": "Deletes the specified UserID within the collection.", "id": "deleteuser-1686181913475", "title": "DeleteUser"}], "DescribeDataset": [{"input": {"DatasetArn": "arn:aws:rekognition:us-east-1:************:project/my-project/dataset/train/1690476084535"}, "output": {"DatasetDescription": {"CreationTimestamp": "2023-07-27T09:41:24.738000-07:00", "DatasetStats": {"ErrorEntries": 0, "LabeledEntries": 15, "TotalEntries": 15, "TotalLabels": 9}, "LastUpdatedTimestamp": "2023-07-28T09:46:45.406000-07:00", "Status": "UPDATE_FAILED", "StatusMessage": "The manifest file contains images from multiple S3 buckets.", "StatusMessageCode": "CLIENT_ERROR"}}, "comments": {"input": {}, "output": {}}, "description": "Describes an Amazon Rekognition Custom Labels dataset.", "id": "to-describe-an-amazon-rekognition-custom-labels-dataset-1690827075604", "title": "To describe an Amazon Rekognition Custom Labels dataset"}], "DescribeProjectVersions": [{"input": {"ProjectArn": "arn:aws:rekognition:us-east-1:************:project/my-project/*************", "VersionNames": ["1"]}, "output": {"NextToken": "", "ProjectVersionDescriptions": [{"BillableTrainingTimeInSeconds": 1899, "CreationTimestamp": "2023-07-28T08:05:51.958000-07:00", "EvaluationResult": {"F1Score": 1, "Summary": {"S3Object": {"Bucket": "custom-labels-console-us-east-1-111111111", "Name": "my-model-output/EvaluationResultSummary-my-project-1.json"}}}, "ManifestSummary": {"S3Object": {"Bucket": "custom-labels-console-us-east-1-111111111", "Name": "my-model-output/ManifestSummary-my-project-1.json"}}, "OutputConfig": {"S3Bucket": "custom-labels-console-us-east-1-111111111", "S3KeyPrefix": "my-model-output"}, "ProjectVersionArn": "arn:aws:rekognition:us-east-1:************:project/my-project/version/1/1690556751958", "Status": "TRAINING_COMPLETED", "StatusMessage": "The model is ready to run.", "TestingDataResult": {"Input": {"AutoCreate": false}, "Output": {"Assets": [{"GroundTruthManifest": {"S3Object": {"Bucket": "custom-labels-console-us-east-1-111111111", "Name": "my-model-output/TestingGroundTruth-my-project-1.json"}}}], "AutoCreate": false}, "Validation": {"Assets": [{"GroundTruthManifest": {"S3Object": {"Bucket": "custom-labels-console-us-east-1-111111111", "Name": "my-model-output/TestingManifestWithValidation-my-project-1.json"}}}]}}, "TrainingDataResult": {"Input": {}, "Output": {"Assets": [{"GroundTruthManifest": {"S3Object": {"Bucket": "custom-labels-console-us-east-1-111111111", "Name": "my-model-output/TrainingGroundTruth-my-project-1.json"}}}]}, "Validation": {"Assets": [{"GroundTruthManifest": {"S3Object": {"Bucket": "custom-labels-console-us-east-1-111111111", "Name": "my-model-output/TrainingManifestWithValidation-my-project-1.json"}}}]}}, "TrainingEndTimestamp": "2023-07-28T08:33:10.827000-07:00"}]}, "comments": {"input": {}}, "description": "Describes a version of an Amazon Rekognition Custom Labels model.", "id": "to-describes-an-amazon-rekognition-custom-labels-model-1690558265950", "title": "To describe an Amazon Rekognition Custom Labels model"}], "DescribeProjects": [{"input": {"ProjectNames": ["my-project"]}, "output": {"ProjectDescriptions": [{"CreationTimestamp": "2022-06-13T15:16:00.634000-07:00", "Datasets": [{"CreationTimestamp": "2022-06-13T15:17:34.620000-07:00", "DatasetArn": "arn:aws:rekognition:us-east-1:************:project/my-project/dataset/test/1655158654491", "DatasetType": "TEST", "Status": "CREATE_COMPLETE", "StatusMessage": "The dataset was successfully created from the manifest file.", "StatusMessageCode": "SUCCESS"}, {"CreationTimestamp": "2022-06-13T15:17:50.118000-07:00", "DatasetArn": "arn:aws:rekognition:us-east-1:************:project/my-project/dataset/train/1655158669954", "DatasetType": "TRAIN", "Status": "CREATE_COMPLETE", "StatusMessage": "The dataset was successfully created from the manifest file.", "StatusMessageCode": "SUCCESS"}], "ProjectArn": "arn:aws:rekognition:us-east-1:************:project/my-project/1655158560634", "Status": "CREATED"}]}, "comments": {"input": {}, "output": {}}, "description": "Describes an Amazon Rekognition Custom Labels projects.", "id": "to-describe-an-amazon-rekognition-custom-labels-project-1690400952376", "title": "To describe an Amazon Rekognition Custom Labels project."}], "DetectCustomLabels": [{"input": {"Image": {"S3Object": {"Bucket": "custom-labels-console-us-east-1-1111111111", "Name": "assets/flowers_1_test_dataset/camellia4.jpg"}}, "MaxResults": 100, "MinConfidence": 50, "ProjectVersionArn": "arn:aws:rekognition:us-east-1:************:project/my-project/version/my-project.2023-07-31T11.49.37/1690829378219"}, "output": {"CustomLabels": [{"Confidence": 67.56399536132812, "Name": "with_leaves"}, {"Confidence": 50.65699768066406, "Name": "without_leaves"}]}, "comments": {"input": {}, "output": {}}, "description": "Detects custom labels in an image with an Amazon Rekognition Custom Labels model", "id": "to-detect-custom-labels-in-images-with-an-amazon-rekognition-custom-labels-model-1690832037913", "title": "To detect custom labels in an image with an Amazon Rekognition Custom Labels model"}], "DetectFaces": [{"input": {"Image": {"S3Object": {"Bucket": "mybucket", "Name": "myphoto"}}}, "output": {"FaceDetails": [{"BoundingBox": {"Height": 0.18000000715255737, "Left": 0.5555555820465088, "Top": 0.33666667342185974, "Width": 0.23999999463558197}, "Confidence": 100, "Landmarks": [{"Type": "eyeLeft", "X": 0.6394737362861633, "Y": 0.40819624066352844}, {"Type": "eyeRight", "X": 0.7266660928726196, "Y": 0.41039225459098816}, {"Type": "eyeRight", "X": 0.6912462115287781, "Y": 0.44240960478782654}, {"Type": "mouthDown", "X": 0.6306198239326477, "Y": 0.46700039505958557}, {"Type": "mouthUp", "X": 0.7215608954429626, "Y": 0.47114261984825134}], "Pose": {"Pitch": 4.050806522369385, "Roll": 0.9950747489929199, "Yaw": 13.693790435791016}, "Quality": {"Brightness": 37.60169982910156, "Sharpness": 80}}], "OrientationCorrection": "ROTATE_0"}, "comments": {"input": {}, "output": {}}, "description": "This operation detects faces in an image stored in an AWS S3 bucket.", "id": "to-detect-faces-in-an-image-1481841782793", "title": "To detect faces in an image"}], "DetectLabels": [{"input": {"Image": {"S3Object": {"Bucket": "mybucket", "Name": "myphoto"}}, "MaxLabels": 123, "MinConfidence": 70}, "output": {"Labels": [{"Confidence": 99.25072479248047, "Name": "People"}, {"Confidence": 99.25074005126953, "Name": "Person"}]}, "comments": {"input": {}, "output": {}}, "description": "This operation detects labels in the supplied image", "id": "to-detect-labels-1481834255770", "title": "To detect labels"}], "DisassociateFaces": [{"input": {"ClientRequestToken": "550e8400-e29b-41d4-a716-446655440003", "CollectionId": "MyCollection", "FaceIds": ["f5817d37-94f6-4335-bfee-6cf79a3d806e", "c92265d4-5f9c-43af-a58e-12be0ce02bc3"], "UserId": "DemoUser"}, "output": {"DisassociatedFaces": [{"FaceId": "c92265d4-5f9c-43af-a58e-12be0ce02bc3"}], "UnsuccessfulFaceDisassociations": [{"FaceId": "f5817d37-94f6-4335-bfee-6cf79a3d806e", "Reasons": ["ASSOCIATED_TO_A_DIFFERENT_USER"], "UserId": "demoUser1"}], "UserStatus": "UPDATING"}, "comments": {"input": {}, "output": {}}, "description": "Removes the association between a Face supplied in an array of FaceIds and the User.", "id": "disassociatefaces-1686182627295", "title": "DisassociateFaces"}], "DistributeDatasetEntries": [{"input": {"Datasets": [{"Arn": "arn:aws:rekognition:us-east-1:************:project/my-proj-2/dataset/train/1690564858106"}, {"Arn": "arn:aws:rekognition:us-east-1:************:project/my-proj-2/dataset/test/1690564858106"}]}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "Distributes an Amazon Rekognition Custom Labels training dataset to a test dataset.", "id": "to-distribute-to-an-amazon-rekognition-custom-labels-dataset-1690816977073", "title": "To distribute an Amazon Rekognition Custom Labels dataset"}], "GetMediaAnalysisJob": [{"input": {"JobId": "861a0645d98ef88efb75477628c011c04942d9d5f58faf2703c393c8cf8c1537"}, "output": {"CompletionTimestamp": "2023-07-28T08:05:51.958000-07:00", "CreationTimestamp": "2023-07-28T08:05:51.958000-06:00", "Input": {"S3Object": {"Bucket": "input-bucket", "Name": "input-manifest.json"}}, "JobId": "861a0645d98ef88efb75477628c011c04942d9d5f58faf2703c393c8cf8c1537", "JobName": "job-name", "ManifestSummary": {"S3Object": {"Bucket": "output-bucket", "Name": "output-location/861a0645d98ef88efb75477628c011c04942d9d5f58faf2703c393c8cf8c1537-manifest-summary.json"}}, "OperationsConfig": {"DetectModerationLabels": {"MinConfidence": 50, "ProjectVersion": "arn:aws:rekognition:us-east-1:************:project/my-project/version/1/1690556751958"}}, "OutputConfig": {"S3Bucket": "output-bucket", "S3KeyPrefix": "output-location"}, "Results": {"S3Object": {"Bucket": "output-bucket", "Name": "output-location/861a0645d98ef88efb75477628c011c04942d9d5f58faf2703c393c8cf8c1537-results.jsonl"}}, "Status": "SUCCEEDED"}, "comments": {"input": {}, "output": {}}, "description": "Retrieves the results for a given media analysis job.", "id": "getmediaanalysisjob-1697650068124", "title": "GetMediaAnalysisJob"}], "IndexFaces": [{"input": {"CollectionId": "myphotos", "DetectionAttributes": [], "ExternalImageId": "myphotoid", "Image": {"S3Object": {"Bucket": "mybucket", "Name": "myphoto"}}}, "output": {"FaceRecords": [{"Face": {"BoundingBox": {"Height": 0.33481481671333313, "Left": 0.31888890266418457, "Top": 0.4933333396911621, "Width": 0.25}, "Confidence": 99.9991226196289, "FaceId": "ff43d742-0c13-5d16-a3e8-03d3f58e980b", "ImageId": "465f4e93-763e-51d0-b030-b9667a2d94b1"}, "FaceDetail": {"BoundingBox": {"Height": 0.33481481671333313, "Left": 0.31888890266418457, "Top": 0.4933333396911621, "Width": 0.25}, "Confidence": 99.9991226196289, "Landmarks": [{"Type": "eyeLeft", "X": 0.3976764678955078, "Y": 0.6248345971107483}, {"Type": "eyeRight", "X": 0.4810936450958252, "Y": 0.6317117214202881}, {"Type": "noseLeft", "X": 0.41986238956451416, "Y": 0.7111940383911133}, {"Type": "mouthDown", "X": 0.40525302290916443, "Y": 0.7497701048851013}, {"Type": "mouthUp", "X": 0.4753248989582062, "Y": 0.7558549642562866}], "Pose": {"Pitch": -9.713645935058594, "Roll": 4.707281112670898, "Yaw": -24.438663482666016}, "Quality": {"Brightness": 29.23358917236328, "Sharpness": 80}}}, {"Face": {"BoundingBox": {"Height": 0.32592591643333435, "Left": 0.5144444704055786, "Top": 0.15111111104488373, "Width": 0.24444444477558136}, "Confidence": 99.99950408935547, "FaceId": "8be04dba-4e58-520d-850e-9eae4af70eb2", "ImageId": "465f4e93-763e-51d0-b030-b9667a2d94b1"}, "FaceDetail": {"BoundingBox": {"Height": 0.32592591643333435, "Left": 0.5144444704055786, "Top": 0.15111111104488373, "Width": 0.24444444477558136}, "Confidence": 99.99950408935547, "Landmarks": [{"Type": "eyeLeft", "X": 0.6006892323493958, "Y": 0.290842205286026}, {"Type": "eyeRight", "X": 0.6808141469955444, "Y": 0.29609042406082153}, {"Type": "noseLeft", "X": 0.6395332217216492, "Y": 0.3522595763206482}, {"Type": "mouthDown", "X": 0.5892083048820496, "Y": 0.38689887523651123}, {"Type": "mouthUp", "X": 0.674560010433197, "Y": 0.394125759601593}], "Pose": {"Pitch": -4.683138370513916, "Roll": 2.1029529571533203, "Yaw": 6.716655254364014}, "Quality": {"Brightness": 34.951698303222656, "Sharpness": 160}}}], "OrientationCorrection": "ROTATE_0"}, "comments": {"input": {}, "output": {}}, "description": "This operation detects faces in an image and adds them to the specified Rekognition collection.", "id": "to-add-a-face-to-a-collection-1482179542923", "title": "To add a face to a collection"}], "ListCollections": [{"input": {}, "output": {"CollectionIds": ["myphotos"]}, "comments": {"input": {}, "output": {}}, "description": "This operation returns a list of Rekognition collections.", "id": "to-list-the-collections-1482179199088", "title": "To list the collections"}], "ListDatasetEntries": [{"input": {"ContainsLabels": ["camellia"], "DatasetArn": "arn:aws:rekognition:us-east-1:************:project/my-proj-2/dataset/train/1690564858106", "HasErrors": true, "Labeled": true, "MaxResults": 100, "NextToken": "", "SourceRefContains": "camellia4.jpg"}, "output": {"DatasetEntries": ["{\"source-ref\":\"s3://custom-labels-console-us-east-1-1111111111/assets/flowers_1_train_dataset/camellia4.jpg\",\"camellia\":1,\"camellia-metadata\":{\"confidence\":1,\"job-name\":\"labeling-job/camellia\",\"class-name\":\"camellia\",\"human-annotated\":\"yes\",\"creation-date\":\"2021-07-11T03:32:13.456Z\",\"type\":\"groundtruth/image-classification\"},\"with_leaves\":1,\"with_leaves-metadata\":{\"confidence\":1,\"job-name\":\"labeling-job/with_leaves\",\"class-name\":\"with_leaves\",\"human-annotated\":\"yes\",\"creation-date\":\"2021-07-11T03:32:13.456Z\",\"type\":\"groundtruth/image-classification\"},\"cl-metadata\":{\"is_labeled\":true}}"], "NextToken": ""}, "comments": {"input": {}, "output": {}}, "description": "Lists the JSON line entries in an Amazon Rekognition Custom Labels dataset.", "id": "to-list-the-entries-in-an-amazon-rekognition-custom-labels-dataset-1690823292345", "title": "To list the entries in an Amazon Rekognition Custom Labels dataset"}], "ListDatasetLabels": [{"input": {"DatasetArn": "arn:aws:rekognition:us-east-1:************:project/my-proj-2/dataset/train/1690564858106", "MaxResults": 100, "NextToken": ""}, "output": {"DatasetLabelDescriptions": [{"LabelName": "camellia", "LabelStats": {"EntryCount": 1}}, {"LabelName": "with_leaves", "LabelStats": {"EntryCount": 2}}, {"LabelName": "mediterranean_spurge", "LabelStats": {"EntryCount": 1}}]}, "comments": {"input": {}, "output": {}}, "description": "Lists the JSON line entries in an Amazon Rekognition Custom Labels dataset.", "id": "to-list-the-entries-in-an-amazon-rekognition-custom-labels-dataset-1690823292345", "title": "To list the entries in an Amazon Rekognition Custom Labels dataset"}], "ListFaces": [{"input": {"CollectionId": "myphotos", "MaxResults": 20}, "output": {"FaceModelVersion": "6.0", "Faces": [{"BoundingBox": {"Height": 0.056759100407361984, "Left": 0.3453829884529114, "Top": 0.36568498611450195, "Width": 0.03177810087800026}, "Confidence": 99.76940155029297, "FaceId": "c92265d4-5f9c-43af-a58e-12be0ce02bc3", "ImageId": "56a0ca74-1c83-39dd-b363-051a64168a65", "IndexFacesModelVersion": "6.0", "UserId": "demoUser2"}, {"BoundingBox": {"Height": 0.06347999721765518, "Left": 0.5160620212554932, "Top": 0.6080359816551208, "Width": 0.03254450112581253}, "Confidence": 99.94369506835938, "FaceId": "851cb847-dccc-4fea-9309-9f4805967855", "ImageId": "a8aed589-ceec-35f7-9c04-82e0b546b024", "IndexFacesModelVersion": "6.0"}, {"BoundingBox": {"Height": 0.05266290158033371, "Left": 0.6513839960098267, "Top": 0.4218429923057556, "Width": 0.03094629943370819}, "Confidence": 99.82969665527344, "FaceId": "c0eb3b65-24a0-41e1-b23a-1908b1aaeac1", "ImageId": "56a0ca74-1c83-39dd-b363-051a64168a65", "IndexFacesModelVersion": "6.0"}]}, "comments": {"input": {}, "output": {}}, "description": "This operation lists the faces in a Rekognition collection.", "id": "to-list-the-faces-in-a-collection-1482181416530", "title": "To list the faces in a collection"}], "ListMediaAnalysisJobs": [{"input": {"MaxResults": 10}, "output": {"MediaAnalysisJobs": [{"CompletionTimestamp": "2023-07-28T08:05:51.958000-07:00", "CreationTimestamp": "2023-07-28T08:05:51.958000-06:00", "Input": {"S3Object": {"Bucket": "input-bucket", "Name": "input-manifest.json"}}, "JobId": "861a0645d98ef88efb75477628c011c04942d9d5f58faf2703c393c8cf8c1537", "JobName": "job-name", "ManifestSummary": {"S3Object": {"Bucket": "output-bucket", "Name": "output-location/861a0645d98ef88efb75477628c011c04942d9d5f58faf2703c393c8cf8c1537-manifest-summary.json"}}, "OperationsConfig": {"DetectModerationLabels": {"MinConfidence": 50, "ProjectVersion": "arn:aws:rekognition:us-east-1:************:project/my-project/version/1/1690556751958"}}, "OutputConfig": {"S3Bucket": "output-bucket", "S3KeyPrefix": "output-location"}, "Results": {"S3Object": {"Bucket": "output-bucket", "Name": "output-location/861a0645d98ef88efb75477628c011c04942d9d5f58faf2703c393c8cf8c1537-results.jsonl"}}, "Status": "SUCCEEDED"}]}, "comments": {"input": {}, "output": {}}, "description": "Returns a list of media analysis jobs.", "id": "listmediaanalysisjobs-1697650653077", "title": "ListMediaAnalysisJobs"}], "ListProjectPolicies": [{"input": {"MaxResults": 5, "NextToken": "", "ProjectArn": "arn:aws:rekognition:us-east-1:************:project/my-sdk-project/*************"}, "output": {"NextToken": "", "ProjectPolicies": [{"CreationTimestamp": "2022-07-01T11:51:27.086000-07:00", "LastUpdatedTimestamp": "2022-07-01T11:51:27.086000-07:00", "PolicyDocument": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Sid\":\"Statemented1\",\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"arn:aws:iam::************:root\"},\"Action\":\"rekognition:CopyProjectVersion\",\"Resource\":\"*\"}]}", "PolicyName": "testPolicy", "PolicyRevisionId": "3b274c25e9203a56a99e00e3ff205fbc", "ProjectArn": "arn:aws:rekognition:us-east-1:************:project/my-sdk-project/*************"}]}, "comments": {"input": {}, "output": {}}, "description": "This operation lists the project policies that are attached to an Amazon Rekognition Custom Labels project.", "id": "listprojectpolicies-1658202290173", "title": "ListProjectPolicies"}], "ListUsers": [{"input": {"CollectionId": "MyCollection"}, "output": {"NextToken": "MGYZLAHX1T5a....", "Users": [{"UserId": "demoUser4", "UserStatus": "CREATED"}, {"UserId": "demoUser2", "UserStatus": "CREATED"}]}, "comments": {"input": {}, "output": {}}, "description": "Returns metadata of the User such as UserID in the specified collection.", "id": "listusers-1686182360075", "title": "ListUsers"}], "PutProjectPolicy": [{"input": {"PolicyDocument": "'{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"ALLOW\",\"Principal\":{\"AWS\":\"principal\"},\"Action\":\"rekognition:CopyProjectVersion\",\"Resource\":\"arn:aws:rekognition:us-east-1:************:project/my-sdk-project/version/DestinationVersionName/*************\"}]}'", "PolicyName": "SamplePolicy", "PolicyRevisionId": "0123456789abcdef", "ProjectArn": "arn:aws:rekognition:us-east-1:************:project/my-sdk-project/*************"}, "output": {"PolicyRevisionId": "0123456789abcdef"}, "comments": {"input": {}, "output": {}}, "description": "This operation attaches a project policy to a Amazon Rekognition Custom Labels project in a trusting AWS account.", "id": "putprojectpolicy-*************", "title": "PutProjectPolicy"}], "SearchFaces": [{"input": {"CollectionId": "myphotos", "FaceId": "70008e50-75e4-55d0-8e80-363fb73b3a14", "FaceMatchThreshold": 90, "MaxFaces": 10}, "output": {"FaceMatches": [{"Face": {"BoundingBox": {"Height": 0.****************, "Left": 0.****************, "Top": 0.*****************, "Width": 0.*****************}, "Confidence": 99.**************, "FaceId": "8be04dba-4e58-520d-850e-9eae4af70eb2", "ImageId": "465f4e93-763e-51d0-b030-b9667a2d94b1"}, "Similarity": 99.**************}, {"Face": {"BoundingBox": {"Height": 0.*****************, "Left": 0.*****************, "Top": 0.****************, "Width": 0.*****************}, "Confidence": 100, "FaceId": "29a75abe-397b-5101-ba4f-706783b2246c", "ImageId": "147fdf82-7a71-52cf-819b-e786c7b9746e"}, "Similarity": 97.**************}, {"Face": {"BoundingBox": {"Height": 0.*****************, "Left": 0.****************, "Top": 0.****************, "Width": 0.*********06378174}, "Confidence": 99.9999008178711, "FaceId": "908544ad-edc3-59df-8faf-6a87cc256cf5", "ImageId": "3c731605-d772-541a-a5e7-0375dbc68a07"}, "Similarity": 95.94520568847656}], "SearchedFaceId": "70008e50-75e4-55d0-8e80-363fb73b3a14"}, "comments": {"input": {}, "output": {}}, "description": "This operation searches for matching faces in the collection the supplied face belongs to.", "id": "to-delete-a-face-1482182799377", "title": "To delete a face"}], "SearchFacesByImage": [{"input": {"CollectionId": "myphotos", "FaceMatchThreshold": 95, "Image": {"S3Object": {"Bucket": "mybucket", "Name": "myphoto"}}, "MaxFaces": 5}, "output": {"FaceMatches": [{"Face": {"BoundingBox": {"Height": 0.3234420120716095, "Left": 0.3233329951763153, "Top": 0.5, "Width": 0.24222199618816376}, "Confidence": 99.99829864501953, "FaceId": "38271d79-7bc2-5efb-b752-398a8d575b85", "ImageId": "d5631190-d039-54e4-b267-abd22c8647c5"}, "Similarity": 99.97036743164062}], "SearchedFaceBoundingBox": {"Height": 0.33481481671333313, "Left": 0.31888890266418457, "Top": 0.4933333396911621, "Width": 0.25}, "SearchedFaceConfidence": 99.9991226196289}, "comments": {"input": {}, "output": {}}, "description": "This operation searches for faces in a Rekognition collection that match the largest face in an S3 bucket stored image.", "id": "to-search-for-faces-matching-a-supplied-image-1482175994491", "title": "To search for faces matching a supplied image"}], "SearchUsers": [{"input": {"CollectionId": "MyCollection", "MaxUsers": 2, "UserId": "DemoUser", "UserMatchThreshold": 70}, "output": {"FaceModelVersion": "6", "SearchedUser": {"UserId": "DemoUser"}, "UserMatches": [{"Similarity": 99.88186645507812, "User": {"UserId": "demoUser1", "UserStatus": "ACTIVE"}}]}, "comments": {"input": {}, "output": {}}, "description": "Searches for UserIDs within a collection based on a FaceId or UserId.", "id": "searchusers-1686182912030", "title": "SearchUsers"}], "SearchUsersByImage": [{"input": {"CollectionId": "MyCollection", "Image": {"S3Object": {"Bucket": "bucket", "Name": "input.jpg"}}, "MaxUsers": 2, "QualityFilter": "MEDIUM", "UserMatchThreshold": 70}, "output": {"FaceModelVersion": "6", "SearchedFace": {"FaceDetail": {"BoundingBox": {"Height": 0.07510016113519669, "Left": 0.3598678708076477, "Top": 0.5391526818275452, "Width": 0.03692837432026863}}}, "UnsearchedFaces": [{"FaceDetails": {"BoundingBox": {"Height": 0.0682177022099495, "Left": 0.6102562546730042, "Top": 0.5593535900115967, "Width": 0.031677018851041794}}, "Reasons": ["FACE_NOT_LARGEST"]}, {"FaceDetails": {"BoundingBox": {"Height": 0.06347997486591339, "Left": 0.516062319278717, "Top": 0.6080358028411865, "Width": 0.03254449740052223}}, "Reasons": ["FACE_NOT_LARGEST"]}], "UserMatches": [{"Similarity": 99.88186645507812, "User": {"UserId": "demoUser1", "UserStatus": "ACTIVE"}}]}, "comments": {"input": {}, "output": {}}, "description": "Searches for UserIDs using a supplied image.", "id": "searchusersbyimage-1686183178610", "title": "SearchUsersByImage"}], "StartMediaAnalysisJob": [{"input": {"Input": {"S3Object": {"Bucket": "input-bucket", "Name": "input-manifest.json"}}, "JobName": "job-name", "OperationsConfig": {"DetectModerationLabels": {"MinConfidence": 50, "ProjectVersion": "arn:aws:rekognition:us-east-1:************:project/my-project/version/1/1690556751958"}}, "OutputConfig": {"S3Bucket": "output-bucket", "S3KeyPrefix": "output-location"}}, "output": {"JobId": "861a0645d98ef88efb75477628c011c04942d9d5f58faf2703c393c8cf8c1537"}, "comments": {"input": {}, "output": {}}, "description": "Initiates a new media analysis job.", "id": "startmediaanalysisjob-1697651090922", "title": "StartMediaAnalysisJob"}], "StartProjectVersion": [{"input": {"MaxInferenceUnits": 1, "MinInferenceUnits": 1, "ProjectVersionArn": "arn:aws:rekognition:us-east-1:************:project/my-project/version/1/1690556751958"}, "output": {"Status": "STARTING"}, "comments": {"input": {}, "output": {}}, "description": "Starts a version of an Amazon Rekognition Custom Labels model.", "id": "to-start-an-amazon-rekognition-custom-labels-model-1690559168398", "title": "To start an Amazon Rekognition Custom Labels model"}], "StopProjectVersion": [{"input": {"ProjectVersionArn": "arn:aws:rekognition:us-east-1:************:project/my-project/version/1/1690556751958"}, "output": {"Status": "STOPPING"}, "comments": {"input": {}, "output": {}}, "description": "Stops a version of an Amazon Rekognition Custom Labels model.", "id": "to-stop-an-amazon-rekognition-custom-labels-model-1690561110698", "title": "To stop an Amazon Rekognition Custom Labels model."}], "UpdateDatasetEntries": [{"input": {"Changes": {"GroundTruth": "{\"source-ref\":\"s3://custom-labels-console-us-east-1-111111111/assets/flowers_1_test_dataset/mediterranean_spurge4.jpg\",\"mediterranean_spurge\":1,\"mediterranean_spurge-metadata\":{\"confidence\":1,\"job-name\":\"labeling-job/mediterranean_spurge\",\"class-name\":\"mediterranean_spurge\",\"human-annotated\":\"yes\",\"creation-date\":\"2021-07-11T03:33:42.025Z\",\"type\":\"groundtruth/image-classification\"},\"with_leaves\":1,\"with_leaves-metadata\":{\"confidence\":1,\"job-name\":\"labeling-job/with_leaves\",\"class-name\":\"with_leaves\",\"human-annotated\":\"yes\",\"creation-date\":\"2021-07-11T03:33:42.025Z\",\"type\":\"groundtruth/image-classification\"}}"}, "DatasetArn": "arn:aws:rekognition:us-east-1:************:project/my-proj-2/dataset/train/1690564858106"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "Adds dataset entries to an Amazon Rekognition Custom Labels dataset.", "id": "to-add-dataset-entries-to-an-amazon-rekognition-custom-labels-dataset-1690816977073", "title": "To-add dataset entries to an Amazon Rekognition Custom Labels dataset"}]}}