{"pagination": {"DescribeProjectVersions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "ProjectVersionDescriptions"}, "DescribeProjects": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "ProjectDescriptions"}, "GetCelebrityRecognition": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "GetContentModeration": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "GetFaceDetection": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "GetFaceSearch": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "GetLabelDetection": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "GetPersonTracking": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "GetSegmentDetection": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "GetTextDetection": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "ListCollections": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "CollectionIds"}, "ListDatasetEntries": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "DatasetEntries"}, "ListDatasetLabels": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "DatasetLabelDescriptions"}, "ListFaces": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Faces"}, "ListMediaAnalysisJobs": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "ListProjectPolicies": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "ProjectPolicies"}, "ListStreamProcessors": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "ListUsers": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Users"}}}