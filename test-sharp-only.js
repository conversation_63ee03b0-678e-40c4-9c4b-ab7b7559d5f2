const sharp = require("sharp")
const fs = require("fs")
const { RESIZE_OPTIONS, IMAGE_TYPE } = require("./constants")

const testSharpProcessing = async () => {
    const inputPath = "./downloaded-image.jpeg"
    const outputPath = "./sharp-test-output.jpeg"
    
    console.log("🖼️ Testing Sharp processing...")
    
    try {
        // Check if input file exists
        if (!fs.existsSync(inputPath)) {
            console.error("❌ Input file not found:", inputPath)
            console.log("💡 Run 'node download-image.js' first to download the image")
            return
        }
        
        const stats = fs.statSync(inputPath)
        console.log("📊 Input file size:", stats.size, "bytes")
        
        // Test image info
        console.log("\n📋 Getting image metadata...")
        const metadata = await sharp(inputPath).metadata()
        console.log("Image metadata:", {
            format: metadata.format,
            width: metadata.width,
            height: metadata.height,
            channels: metadata.channels,
            density: metadata.density,
            hasAlpha: metadata.hasAlpha,
            hasProfile: metadata.hasProfile
        })
        
        // Test resize options
        const options = RESIZE_OPTIONS[IMAGE_TYPE.LOGO]
        console.log("\n⚙️ Resize options:", options)
        
        if (!options) {
            console.error("❌ No resize options found!")
            return
        }
        
        // Test Sharp processing
        console.log("\n🔄 Processing with Sharp...")
        
        const startTime = Date.now()
        
        await sharp(inputPath)
            .keepExif()
            .resize(options)
            .jpeg({
                force: true,
            })
            .toFile(outputPath)
        
        const endTime = Date.now()
        console.log("✅ Sharp processing completed in", endTime - startTime, "ms")
        
        // Check output
        const outputStats = fs.statSync(outputPath)
        console.log("📊 Output file size:", outputStats.size, "bytes")
        
        if (outputStats.size === 0) {
            console.error("❌ Output file is empty!")
        } else {
            console.log("✅ Output file created successfully:", outputPath)
            
            // Get output metadata
            const outputMetadata = await sharp(outputPath).metadata()
            console.log("Output metadata:", {
                format: outputMetadata.format,
                width: outputMetadata.width,
                height: outputMetadata.height,
                size: outputStats.size
            })
        }
        
    } catch (error) {
        console.error("💥 Sharp processing failed:", {
            message: error.message,
            stack: error.stack
        })
        
        // Additional error analysis
        if (error.message.includes("Input file contains unsupported image format")) {
            console.log("💡 The image format is not supported by Sharp")
        } else if (error.message.includes("Input file has corrupt header")) {
            console.log("💡 The image file appears to be corrupted")
        } else if (error.message.includes("Input buffer contains unsupported image format")) {
            console.log("💡 The image data is corrupted or in an unsupported format")
        }
    }
}

testSharpProcessing().catch(console.error)
