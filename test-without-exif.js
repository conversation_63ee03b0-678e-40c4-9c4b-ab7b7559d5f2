const sharp = require("sharp")
const fs = require("fs")
const { RESIZE_OPTIONS, IMAGE_TYPE } = require("./constants")

const testWithoutExif = async () => {
    const inputPath = "./downloaded-image.jpeg"
    const outputPath = "./no-exif-output.jpeg"
    
    console.log("🖼️ Testing Sharp processing WITHOUT keepExif()...")
    
    try {
        if (!fs.existsSync(inputPath)) {
            console.error("❌ Input file not found:", inputPath)
            return
        }
        
        const options = RESIZE_OPTIONS[IMAGE_TYPE.LOGO]
        console.log("⚙️ Resize options:", options)
        
        console.log("🔄 Processing without keepExif()...")
        
        await sharp(inputPath)
            .resize(options)  // Remove .keepExif()
            .jpeg({
                force: true,
            })
            .toFile(outputPath)
        
        console.log("✅ Processing completed successfully!")
        
        const outputStats = fs.statSync(outputPath)
        console.log("📊 Output file size:", outputStats.size, "bytes")
        
    } catch (error) {
        console.error("💥 Processing failed even without EXIF:", error.message)
    }
}

testWithoutExif().catch(console.error)
