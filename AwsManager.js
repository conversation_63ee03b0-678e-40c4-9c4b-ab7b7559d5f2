const stream = require("stream")
const AWS = require("aws-sdk")

const {
    CONTENT_TYPE_BY_MAP,
    CREDENTIALS,
} = require("./constants")

const { logTimeMessage } = require("./Utils")

const S3 = new AWS.S3(CREDENTIALS)

// create the read stream abstraction for downloading data from S3
const readStreamFromS3 = (Key) => {
    const startTime = performance.now()

    const readStream =
        S3
            .getObject({
                Key,
                Bucket: CREDENTIALS.Bucket,
            })
            .createReadStream()

    const endTime = performance.now()
    logTimeMessage("readStreamFromS3", startTime, endTime)

    return readStream
}

// create the write stream abstraction for uploading data to S3
const writeStreamToS3 = (Key, ext) => {
    const pass = new stream.PassThrough()
    const ContentType = CONTENT_TYPE_BY_MAP[ext] || undefined

    return {
        writeStream: pass,
        uploadFinished:
            S3
                .upload({
                    Body: pass,
                    Bucket: CREDENTIALS.Bucket,
                    Key,
                    ContentType,
                })
                .promise()
    }
}

module.exports = {
    readStreamFromS3,
    writeStreamToS3,
}
