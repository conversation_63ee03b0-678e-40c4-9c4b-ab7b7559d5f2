exports.CREDENTIALS = {
    accessKeyId: process.env.ACCESS_KEY,
    secretAccessKey: process.env.SECRET_KEY,
    Bucket: process.env.BUCKET_NAME,
    region: process.env.REGION,
    signatureVersion: "v4"
}

exports.IMAGE_TYPE = {
    LOGO: "LOGO",
    THUMBNAIL: "THUMBNAIL",
    MINI_THUMBNAIL: "MINI_THUMBNAIL",

    USER_PROFILE_THUMBNAIL: "USER_PROFILE_THUMBNAIL",
    SYSTEM_USER_PROFILE_THUMBNAIL: "SYSTEM_USER_PROFILE_THUMBNAIL",
}

exports.IMAGE_PATH = {
    MOBILE: "/mobile/",
    LOGO: "/logo/",
    THUMBNAIL: "/thumbnail/",
    MINI_THUMBNAIL: "/mini_thumbnail/",

    // User images
    USER_PROFILE: "/user/profile/",
    SYSTEM_USER_PROFILE: "/user/system-user-profile/",

    COMPRESSED: {
        USER_PROFILE_THUMBNAIL: "/user/compressed-profile/",
        SYSTEM_USER_PROFILE_THUMBNAIL: "/user/compressed-system-user-profile/",
    }
}

exports.EXTENSIONS = {
    PNG: ".png",
    JPEG: ".jpeg",
    JPG: ".jpg",
}

exports.CONTENT_TYPE_BY_MAP = {
    ".png": "image/png",
    ".jpeg": "image/jpeg",
    ".jpg": "image/jpeg",
}

// Common resize options
const thumbnailOptions = {
    width: 50,
    height: 50,
    fit: "contain",
}

//Ref: https://sharp.pixelplumbing.com/api-resize
exports.RESIZE_OPTIONS = {
    "LOGO": {
        width: 300,
        height: 300,
        fit: "contain",
        background: "rgb(255,255,255)",
    },
    "THUMBNAIL": {
        ...thumbnailOptions,
        background: "rgb(255,255,255)",
    },
    "MINI_THUMBNAIL": {
        width: 24,
        height: 24,
        fit: "contain",
        background: "rgb(255,255,255)",
    },
    "USER_PROFILE_THUMBNAIL": thumbnailOptions,
    "SYSTEM_USER_PROFILE_THUMBNAIL": thumbnailOptions,
}
