require("dotenv").config()

const path = require("path")
const fs = require("fs")

const {
    IMAGE_TYPE,
    IMAGE_PATH,
    RESIZE_OPTIONS,
} = require("./constants")

const {
    readStreamFromS3,
    writeStreamToS3,
} = require("./AwsManager")

const {
    streamToSharp,
    logTimeMessage,
} = require("./Utils")

// Test the specific problematic image
const testSpecificImage = async () => {
    const originalKey = "hawak-dev/product/mobile/1131/686e52fa5907c74a61c5a84b_P3.jpeg"
    
    console.log("🧪 Testing specific image:", originalKey)
    
    try {
        // Step 1: Test path detection
        console.log("\n📍 Step 1: Testing path detection...")
        const isMobileCompression = originalKey.includes(IMAGE_PATH.MOBILE)
        console.log("Path detection result:", {
            originalKey,
            mobilePath: IMAGE_PATH.MOBILE,
            isMobileCompression
        })
        
        if (!isMobileCompression) {
            console.error("❌ Image not detected as mobile image!")
            return
        }
        
        // Step 2: Test key generation
        console.log("\n🔑 Step 2: Testing key generation...")
        const logoKey = originalKey.replace(IMAGE_PATH.MOBILE, IMAGE_PATH.LOGO)
        const thumbnailKey = originalKey.replace(IMAGE_PATH.MOBILE, IMAGE_PATH.THUMBNAIL)
        const miniThumbnailKey = originalKey.replace(IMAGE_PATH.MOBILE, IMAGE_PATH.MINI_THUMBNAIL)
        
        console.log("Generated keys:", {
            logoKey,
            thumbnailKey,
            miniThumbnailKey
        })
        
        // Step 3: Test S3 read access
        console.log("\n📥 Step 3: Testing S3 read access...")
        const readStream = readStreamFromS3(originalKey)
        
        // Test if we can read the file
        let dataReceived = false
        let errorOccurred = false
        
        readStream.on('data', (chunk) => {
            if (!dataReceived) {
                console.log("✅ Successfully reading data from S3")
                console.log("First chunk size:", chunk.length, "bytes")
                dataReceived = true
            }
        })
        
        readStream.on('error', (error) => {
            console.error("❌ S3 Read Error:", {
                message: error.message,
                code: error.code,
                statusCode: error.statusCode,
                stack: error.stack
            })
            errorOccurred = true
        })
        
        readStream.on('end', () => {
            console.log("📄 S3 read stream ended")
        })
        
        // Step 4: Test Sharp processing
        console.log("\n🖼️ Step 4: Testing Sharp processing...")
        const ext = path.extname(logoKey)
        const options = RESIZE_OPTIONS[IMAGE_TYPE.LOGO]
        
        console.log("Sharp configuration:", {
            ext,
            imageType: IMAGE_TYPE.LOGO,
            options
        })
        
        if (!options) {
            console.error("❌ No resize options found for LOGO type!")
            return
        }
        
        const sharpStream = streamToSharp(ext, options)
        
        sharpStream.on('error', (error) => {
            console.error("❌ Sharp Processing Error:", {
                message: error.message,
                stack: error.stack
            })
            errorOccurred = true
        })
        
        // Step 5: Test the full pipeline with a local file output
        console.log("\n🔗 Step 5: Testing full pipeline...")
        
        const localOutputPath = `./test-output-${IMAGE_TYPE.LOGO}.jpeg`
        const writeStream = fs.createWriteStream(localOutputPath)
        
        writeStream.on('error', (error) => {
            console.error("❌ Local Write Error:", error.message)
            errorOccurred = true
        })
        
        writeStream.on('finish', () => {
            console.log("✅ Local file written successfully:", localOutputPath)
            
            // Check file size
            const stats = fs.statSync(localOutputPath)
            console.log("Output file size:", stats.size, "bytes")
            
            if (stats.size === 0) {
                console.error("❌ Output file is empty!")
            } else {
                console.log("✅ Output file has content")
            }
        })
        
        // Connect the streams
        const pipeline = readStream
            .pipe(sharpStream)
            .pipe(writeStream)
        
        // Wait for completion
        await new Promise((resolve, reject) => {
            pipeline.on('finish', resolve)
            pipeline.on('error', reject)
            
            // Timeout after 30 seconds
            setTimeout(() => {
                reject(new Error("Pipeline timeout after 30 seconds"))
            }, 30000)
        })
        
        if (!errorOccurred) {
            console.log("\n🎉 Test completed successfully!")
        } else {
            console.log("\n⚠️ Test completed with errors - check logs above")
        }
        
    } catch (error) {
        console.error("\n💥 Test failed with error:", {
            message: error.message,
            stack: error.stack,
            code: error.code
        })
    }
}

// Test configuration
const testConfiguration = () => {
    console.log("🔧 Testing configuration...")
    console.log("IMAGE_TYPE:", IMAGE_TYPE)
    console.log("IMAGE_PATH:", IMAGE_PATH)
    console.log("RESIZE_OPTIONS:", RESIZE_OPTIONS)
    console.log("Environment variables:", {
        ACCESS_KEY: process.env.ACCESS_KEY ? "✅ Set" : "❌ Missing",
        SECRET_KEY: process.env.SECRET_KEY ? "✅ Set" : "❌ Missing",
        BUCKET_NAME: process.env.BUCKET_NAME,
        REGION: process.env.REGION
    })
}

// Run tests
const runTests = async () => {
    console.log("🚀 Starting local image processing test...\n")
    
    testConfiguration()
    await testSpecificImage()
    
    console.log("\n✨ Test completed!")
}

// Handle unhandled rejections
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason)
})

// Run the test
runTests().catch(console.error)
